# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
PGDATABASE=postgres
PGHOST=db.pwaqpoqjmxxlcrxtbjkw.supabase.co
PGPASSWORD=LearnHub2025
PGPORT=5432
PGUSER=postgres

# Authentication Configuration
SESSION_SECRET=your-generated-session-secret-at-least-32-characters
REPLIT_DOMAINS=your-app.amplifyapp.com,your-custom-domain.com
ISSUER_URL=https://replit.com/oidc
REPL_ID=your-amplify-app-id

# External API Keys
YOUTUBE_API_KEY=your-youtube-api-key
SENDGRID_API_KEY=your-sendgrid-api-key
OPENAI_API_KEY=your-openai-api-key

# Application Configuration
NODE_ENV=production
PORT=5000