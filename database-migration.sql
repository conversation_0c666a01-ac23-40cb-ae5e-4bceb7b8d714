-- Database Migration Script for Aurora Serverless
-- This script will help you set up the database structure in Aurora

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create database schema (tables will be created by Drizzle migrations)
-- Run this after importing your data dump

-- Verify migration
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- Check row counts to verify data migration
SELECT 
    'users' as table_name, 
    COUNT(*) as row_count 
FROM users
UNION ALL
SELECT 
    'learning_plans' as table_name, 
    COUNT(*) as row_count 
FROM learning_plans
UNION ALL
SELECT 
    'videos' as table_name, 
    COUNT(*) as row_count 
FROM videos
UNION ALL
SELECT 
    'plan_videos' as table_name, 
    COUNT(*) as row_count 
FROM plan_videos
UNION ALL
SELECT 
    'sessions' as table_name, 
    COUNT(*) as row_count 
FROM sessions;

-- Performance optimization for Aurora
ANALYZE;

-- Create indexes for better performance (if not already created by migrations)
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_learning_plans_user_id ON learning_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_plan_videos_plan_id ON plan_videos(plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_videos_video_id ON plan_videos(video_id);
CREATE INDEX IF NOT EXISTS idx_video_progress_user_id ON video_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expire ON sessions(expire);