-- Migration: Add plan privacy features and professional URLs
-- Date: 2025-01-16

-- Add slug field for professional URLs
ALTER TABLE learning_plans 
ADD COLUMN slug VARCHAR(255) UNIQUE;

-- Create index for slug lookups
CREATE INDEX idx_learning_plans_slug ON learning_plans(slug);

-- Create plan sharing permissions table
CREATE TABLE plan_shares (
    id SERIAL PRIMARY KEY,
    plan_id INTEGER NOT NULL REFERENCES learning_plans(id) ON DELETE CASCADE,
    shared_by_user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    shared_with_user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    can_add_videos BOOLEAN DEFAULT FALSE,
    can_reshare BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(plan_id, shared_with_user_id)
);

-- Create indexes for plan sharing
CREATE INDEX idx_plan_shares_plan_id ON plan_shares(plan_id);
CREATE INDEX idx_plan_shares_shared_with ON plan_shares(shared_with_user_id);

-- Generate slugs for existing plans (update existing data)
UPDATE learning_plans 
SET slug = LOWER(
    REGEXP_REPLACE(
        REGEXP_REPLACE(title, '[^a-zA-Z0-9\s-]', '', 'g'),
        '\s+', '-', 'g'
    ) || '-' || EXTRACT(EPOCH FROM created_at)::INTEGER
)
WHERE slug IS NULL;

-- Make slug NOT NULL after populating existing data
ALTER TABLE learning_plans 
ALTER COLUMN slug SET NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN learning_plans.slug IS 'URL-friendly unique identifier for professional URLs';
COMMENT ON COLUMN learning_plans.is_public IS 'If true, anyone can view and add videos to this plan';
COMMENT ON TABLE plan_shares IS 'Tracks private plan sharing permissions between users';
COMMENT ON COLUMN plan_shares.can_add_videos IS 'If true, shared user can add videos to the plan';
COMMENT ON COLUMN plan_shares.can_reshare IS 'If true, shared user can share the plan with others (only for public plans)';