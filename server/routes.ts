import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./auth";
import {
  insertLearningPlanSchema,
  insertVideoSchema,
  insertPlanVideoSchema,
  insertVideoProgressSchema,
  friendInvites,
  users,
} from "@shared/schema";
import { z } from "zod";
import { db } from "./db";
import { eq } from "drizzle-orm";

export async function registerRoutes(app: Express): Promise<Server> {
  // Security middleware - block common attack patterns
  app.use((req, res, next) => {
    const suspiciousPatterns = [
      "/actuator",
      "/.env",
      "/wp-admin",
      "/admin",
      "/phpmyadmin",
      "/xmlrpc.php",
    ];

    if (suspiciousPatterns.some((pattern) => req.path.includes(pattern))) {
      console.log(
        `🚨 Blocked suspicious request: ${req.method} ${req.path} from ${req.ip}`
      );
      return res.status(404).end();
    }

    next();
  });

  // Auth middleware
  setupAuth(app);

  // Auth routes
  // Auth routes are now handled in auth.ts

  // Learning Plans routes
  app.get("/api/learning-plans", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const plans = await storage.getUserLearningPlans(userId);
      res.json(plans);
    } catch (error) {
      console.error("Error fetching learning plans:", error);
      res.status(500).json({ message: "Failed to fetch learning plans" });
    }
  });

  app.post("/api/learning-plans", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { title, description, isPublic } = req.body;

      // Generate a unique slug for the plan
      const slug = await storage.generatePlanSlug(title);

      const planData = insertLearningPlanSchema.parse({
        ...req.body,
        userId,
        slug,
        isPublic: isPublic || false,
      });

      const plan = await storage.createLearningPlan(planData);
      res.status(201).json(plan);
    } catch (error) {
      console.error("Error creating learning plan:", error);
      res.status(500).json({ message: "Failed to create learning plan" });
    }
  });

  // Get learning plan by ID (legacy support)
  app.get("/api/learning-plans/:id", isAuthenticated, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      const userId = req.user.id;

      // Check user access permissions
      const permissions = await storage.canUserAccessPlan(planId, userId);
      if (!permissions.canView) {
        return res
          .status(403)
          .json({ message: "Access denied to this learning plan" });
      }

      const plan = await storage.getLearningPlan(planId);
      if (!plan) {
        return res.status(404).json({ message: "Learning plan not found" });
      }

      res.json({ ...plan, permissions });
    } catch (error) {
      console.error("Error fetching learning plan:", error);
      res.status(500).json({ message: "Failed to fetch learning plan" });
    }
  });

  // Get learning plan by slug (professional URLs)
  app.get(
    "/api/learning-plans/slug/:slug",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const { slug } = req.params;
        const userId = req.user.id;

        const plan = await storage.getLearningPlanBySlug(slug);
        if (!plan) {
          return res.status(404).json({ message: "Learning plan not found" });
        }

        // Check user access permissions
        const permissions = await storage.canUserAccessPlan(plan.id, userId);
        if (!permissions.canView) {
          return res
            .status(403)
            .json({ message: "Access denied to this learning plan" });
        }

        res.json({ ...plan, permissions });
      } catch (error) {
        console.error("Error fetching learning plan by slug:", error);
        res.status(500).json({ message: "Failed to fetch learning plan" });
      }
    }
  );

  app.get(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const videos = await storage.getPlanVideos(planId);
        res.json(videos);
      } catch (error) {
        console.error("Error fetching plan videos:", error);
        res.status(500).json({ message: "Failed to fetch plan videos" });
      }
    }
  );

  // YouTube API integration
  app.get("/api/youtube/search", isAuthenticated, async (req: any, res) => {
    try {
      const { q, maxResults = 10 } = req.query;

      if (!q) {
        return res.status(400).json({ message: "Search query is required" });
      }

      const YOUTUBE_API_KEY =
        process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

      if (!YOUTUBE_API_KEY) {
        return res
          .status(500)
          .json({ message: "YouTube API key not configured" });
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${encodeURIComponent(
          q
        )}&maxResults=${maxResults}&key=${YOUTUBE_API_KEY}`
      );

      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.statusText}`);
      }

      const data = await response.json();

      const videos =
        data.items?.map((item: any) => ({
          youtubeId: item.id.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnailUrl:
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default?.url,
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt,
        })) || [];

      res.json(videos);
    } catch (error) {
      console.error("Error searching YouTube:", error);
      res.status(500).json({ message: "Failed to search YouTube videos" });
    }
  });

  // Video routes
  app.post("/api/videos", isAuthenticated, async (req: any, res) => {
    try {
      const videoData = insertVideoSchema.parse(req.body);

      // Check if video already exists
      let video = await storage.getVideoByYoutubeId(videoData.youtubeId);

      if (!video) {
        // Get additional video details from YouTube API
        const YOUTUBE_API_KEY =
          process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

        if (YOUTUBE_API_KEY) {
          const response = await fetch(
            `https://www.googleapis.com/youtube/v3/videos?part=snippet,contentDetails,statistics&id=${videoData.youtubeId}&key=${YOUTUBE_API_KEY}`
          );

          if (response.ok) {
            const data = await response.json();
            const item = data.items?.[0];

            if (item) {
              videoData.duration = item.contentDetails.duration;
              videoData.viewCount = parseInt(item.statistics.viewCount);
            }
          }
        }

        video = await storage.createVideo(videoData);
      }

      res.json(video);
    } catch (error) {
      console.error("Error creating video:", error);
      res.status(500).json({ message: "Failed to create video" });
    }
  });

  // Delete video from library
  app.delete("/api/videos/:id", isAuthenticated, async (req: any, res) => {
    try {
      const videoId = parseInt(req.params.id);
      await storage.deleteVideo(videoId);
      res.json({ message: "Video deleted successfully" });
    } catch (error) {
      console.error("Error deleting video:", error);
      res.status(500).json({ message: "Failed to delete video" });
    }
  });

  app.post(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const { videoId } = req.body;

        console.log("Adding video to plan:", { planId, videoId });

        // Get the current count of videos in the plan to set the order index
        const existingVideos = await storage.getPlanVideos(planId);
        const orderIndex = existingVideos.length;

        const planVideoData = insertPlanVideoSchema.parse({
          planId,
          videoId,
          orderIndex,
        });

        console.log("Parsed plan video data:", planVideoData);

        const planVideo = await storage.addVideoToPlan(planVideoData);
        console.log("Successfully added video to plan:", planVideo);

        res.status(201).json(planVideo);
      } catch (error) {
        console.error("Error adding video to plan:", error);
        res.status(500).json({ message: "Failed to add video to plan" });
      }
    }
  );

  // Get videos for a specific plan
  app.get(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const planVideos = await storage.getPlanVideos(planId);
        res.json(planVideos);
      } catch (error) {
        console.error("Error fetching plan videos:", error);
        res.status(500).json({ message: "Failed to fetch plan videos" });
      }
    }
  );

  // Progress tracking routes
  app.put("/api/video-progress", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      console.log("=== VIDEO PROGRESS UPDATE ===");
      console.log("User ID:", userId);
      console.log("Request body:", JSON.stringify(req.body, null, 2));

      // Check if video exists first
      const video = await storage.getVideo(req.body.videoId);
      if (!video) {
        console.log("Video not found:", req.body.videoId);
        return res.status(404).json({ message: "Video not found" });
      }
      console.log("Video found:", video.title);

      const progressData = insertVideoProgressSchema.parse({
        ...req.body,
        userId,
      });
      console.log(
        "Parsed progress data:",
        JSON.stringify(progressData, null, 2)
      );

      const progress = await storage.updateVideoProgress(progressData);
      console.log(
        "Updated progress successfully:",
        JSON.stringify(progress, null, 2)
      );

      res.json(progress);
    } catch (error) {
      console.error("=== VIDEO PROGRESS ERROR ===");
      console.error("Error updating video progress:", error);
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      res.status(500).json({
        message: "Failed to update video progress",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  app.get(
    "/api/progress/plan/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);

        const progress = await storage.getUserProgressForPlan(userId, planId);
        res.json(progress);
      } catch (error) {
        console.error("Error fetching plan progress:", error);
        res.status(500).json({ message: "Failed to fetch plan progress" });
      }
    }
  );

  // Dashboard analytics routes
  app.get("/api/analytics/daily", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserDailyStats(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching daily stats:", error);
      res.status(500).json({ message: "Failed to fetch daily stats" });
    }
  });

  app.get("/api/analytics/weekly", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserWeeklyProgress(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching weekly progress:", error);
      res.status(500).json({ message: "Failed to fetch weekly progress" });
    }
  });

  app.get("/api/analytics/monthly", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserMonthlyStats(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching monthly stats:", error);
      res.status(500).json({ message: "Failed to fetch monthly stats" });
    }
  });

  // Achievements routes
  app.get("/api/achievements", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const achievements = await storage.getUserAchievements(userId);
      res.json(achievements);
    } catch (error) {
      console.error("Error fetching achievements:", error);
      res.status(500).json({ message: "Failed to fetch achievements" });
    }
  });

  // Continue learning endpoint
  app.get("/api/continue-learning", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;

      // Get user's video progress for videos that are not completed
      const progressData = await storage.getUserIncompleteVideos(userId);
      res.json(progressData);
    } catch (error) {
      console.error("Error fetching continue learning data:", error);
      res
        .status(500)
        .json({ message: "Failed to fetch continue learning data" });
    }
  });

  // Personalized recommendations endpoint
  app.get("/api/recommendations", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const limit = parseInt(req.query.limit as string) || 10;

      const recommendations = await storage.getPersonalizedRecommendations(
        userId,
        limit
      );
      res.json(recommendations);
    } catch (error) {
      console.error("Error fetching recommendations:", error);
      res.status(500).json({ message: "Failed to fetch recommendations" });
    }
  });

  // User watching patterns endpoint
  app.get("/api/user/patterns", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;

      const patterns = await storage.getUserWatchingPatterns(userId);
      res.json(patterns);
    } catch (error) {
      console.error("Error fetching user patterns:", error);
      res.status(500).json({ message: "Failed to fetch user patterns" });
    }
  });

  // Get video by ID
  app.get("/api/videos/:id", isAuthenticated, async (req: any, res) => {
    try {
      const videoId = parseInt(req.params.id);
      const video = await storage.getVideo(videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }
      res.json(video);
    } catch (error) {
      console.error("Error fetching video:", error);
      res.status(500).json({ message: "Failed to fetch video" });
    }
  });

  // Get video progress
  app.get(
    "/api/video-progress/:videoId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const videoId = parseInt(req.params.videoId);
        const progress = await storage.getUserVideoProgress(userId, videoId);
        res.json(progress);
      } catch (error) {
        console.error("Error fetching video progress:", error);
        res.status(500).json({ message: "Failed to fetch video progress" });
      }
    }
  );

  // Delete video from plan
  app.delete(
    "/api/learning-plans/:planId/videos/:videoId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.planId);
        const videoId = parseInt(req.params.videoId);

        await storage.removeVideoFromPlan(planId, videoId);
        res.status(204).send();
      } catch (error) {
        console.error("Error removing video from plan:", error);
        res.status(500).json({ message: "Failed to remove video from plan" });
      }
    }
  );

  // Update learning plan
  app.put("/api/learning-plans/:id", isAuthenticated, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      const updates = req.body;

      const updatedPlan = await storage.updateLearningPlan(planId, updates);
      res.json(updatedPlan);
    } catch (error) {
      console.error("Error updating learning plan:", error);
      res.status(500).json({ message: "Failed to update learning plan" });
    }
  });

  // Delete learning plan
  app.delete(
    "/api/learning-plans/:id",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        await storage.deleteLearningPlan(planId);
        res.status(204).send();
      } catch (error) {
        console.error("Error deleting learning plan:", error);
        res.status(500).json({ message: "Failed to delete learning plan" });
      }
    }
  );

  // Plan sharing routes
  app.post(
    "/api/learning-plans/:id/share",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const shareToken = await storage.generateShareTokenForPlan(planId);
        res.json({
          shareToken,
          shareUrl: `${req.protocol}://${req.hostname}/shared/${shareToken}`,
        });
      } catch (error) {
        console.error("Error generating share token:", error);
        res.status(500).json({ message: "Failed to generate share link" });
      }
    }
  );

  app.get("/api/shared-plan/:shareToken", async (req, res) => {
    try {
      const { shareToken } = req.params;
      const plan = await storage.getLearningPlanByShareToken(shareToken);
      if (!plan) {
        return res.status(404).json({ message: "Plan not found" });
      }

      const planVideos = await storage.getPlanVideos(plan.id);
      res.json({ plan, videos: planVideos });
    } catch (error) {
      console.error("Error fetching shared plan:", error);
      res.status(500).json({ message: "Failed to fetch shared plan" });
    }
  });

  app.post(
    "/api/shared-plan/:shareToken/accept",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const { shareToken } = req.params;
        const userId = req.user.id;

        const newPlan = await storage.copyPlanToUser(shareToken, userId);
        res.json(newPlan);
      } catch (error) {
        console.error("Error accepting shared plan:", error);
        res.status(500).json({ message: "Failed to accept shared plan" });
      }
    }
  );

  // Videos endpoint for explore page
  app.get("/api/videos", async (req, res) => {
    try {
      const allVideos = await storage.getAllVideos();
      res.json(allVideos);
    } catch (error) {
      console.error("Error fetching videos:", error);
      res.status(500).json({ message: "Failed to fetch videos" });
    }
  });

  // YouTube search endpoint
  app.get("/api/youtube/search", async (req, res) => {
    try {
      const { q, pageToken, maxResults = 12 } = req.query;
      const YOUTUBE_API_KEY =
        process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

      if (!YOUTUBE_API_KEY) {
        return res
          .status(500)
          .json({ message: "YouTube API key not configured" });
      }

      // Build URL with optional pageToken for pagination
      let apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=${maxResults}&q=${encodeURIComponent(
        q as string
      )}&type=video&key=${YOUTUBE_API_KEY}`;

      // Add pageToken if provided
      if (pageToken) {
        apiUrl += `&pageToken=${pageToken}`;
      }

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error("YouTube API request failed");
      }

      const data = await response.json();
      const videos =
        data.items?.map((item: any) => ({
          youtubeId: item.id.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnailUrl:
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default?.url,
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt,
        })) || [];

      res.json({ videos });
    } catch (error) {
      console.error("Error searching YouTube:", error);
      res.status(500).json({ message: "Failed to search YouTube" });
    }
  });

  // Recommendations endpoint
  app.get("/api/recommendations", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const recommendations = await storage.getPersonalizedRecommendations(
        userId
      );
      res.json(recommendations);
    } catch (error) {
      console.error("Error fetching recommendations:", error);
      res.status(500).json({ message: "Failed to fetch recommendations" });
    }
  });

  // AI Mood-based recommendations
  app.get(
    "/api/recommendations/mood/:mood/:timeContext",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { mood, timeContext } = req.params;
        const recommendations = await storage.getMoodBasedRecommendations(
          userId,
          mood,
          timeContext
        );
        res.json(recommendations);
      } catch (error) {
        console.error("Error fetching mood recommendations:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch mood recommendations" });
      }
    }
  );

  // User mood profile
  app.get("/api/user/mood-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const profile = await storage.getUserMoodProfile(userId);
      res.json(profile);
    } catch (error) {
      console.error("Error fetching user mood profile:", error);
      res.status(500).json({ message: "Failed to fetch user mood profile" });
    }
  });

  // AI insights
  app.get("/api/ai-insights", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const insights = await storage.getAIInsights(userId);
      res.json(insights);
    } catch (error) {
      console.error("Error fetching AI insights:", error);
      res.status(500).json({ message: "Failed to fetch AI insights" });
    }
  });

  // User patterns endpoint
  app.get("/api/user/patterns", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const patterns = await storage.getUserWatchingPatterns(userId);
      res.json(patterns);
    } catch (error) {
      console.error("Error fetching user patterns:", error);
      res.status(500).json({ message: "Failed to fetch user patterns" });
    }
  });

  // Friend system routes
  app.post("/api/friends/request", isAuthenticated, async (req: any, res) => {
    try {
      const requesterId = req.user.id;
      const { receiverId } = req.body;
      const friendship = await storage.sendFriendRequest(
        requesterId,
        receiverId
      );
      res.json(friendship);
    } catch (error) {
      console.error("Error sending friend request:", error);
      res.status(500).json({ message: "Failed to send friend request" });
    }
  });

  app.post(
    "/api/friends/accept/:friendshipId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const friendshipId = parseInt(req.params.friendshipId);
        const friendship = await storage.acceptFriendRequest(friendshipId);
        res.json(friendship);
      } catch (error) {
        console.error("Error accepting friend request:", error);
        res.status(500).json({ message: "Failed to accept friend request" });
      }
    }
  );

  app.delete(
    "/api/friends/reject/:friendshipId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const friendshipId = parseInt(req.params.friendshipId);
        await storage.rejectFriendRequest(friendshipId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error rejecting friend request:", error);
        res.status(500).json({ message: "Failed to reject friend request" });
      }
    }
  );

  app.get("/api/friends", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const friends = await storage.getFriends(userId);
      res.json(friends);
    } catch (error) {
      console.error("Error fetching friends:", error);
      res.status(500).json({ message: "Failed to fetch friends" });
    }
  });

  app.get("/api/friends/requests", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const requests = await storage.getFriendRequests(userId);
      res.json(requests);
    } catch (error) {
      console.error("Error fetching friend requests:", error);
      res.status(500).json({ message: "Failed to fetch friend requests" });
    }
  });

  app.delete(
    "/api/friends/:friendId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { friendId } = req.params;
        await storage.unfriend(userId, friendId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error unfriending:", error);
        res.status(500).json({ message: "Failed to unfriend" });
      }
    }
  );

  // Friend invite routes
  app.post("/api/friends/invite", isAuthenticated, async (req: any, res) => {
    try {
      const fromUserId = req.user.id;
      const { toEmail } = req.body;

      console.log("Sending friend invite from:", fromUserId, "to:", toEmail);

      // Get sender's info
      const fromUser = await storage.getUser(fromUserId);
      if (!fromUser) {
        return res.status(404).json({ message: "User not found" });
      }

      const invite = await storage.sendFriendInvite(fromUserId, toEmail);
      console.log("Created friend invite:", invite);

      // Send email using SendGrid
      const { sendFriendInviteEmail } = await import("./email");
      const fromUserName = fromUser.firstName || fromUser.email || "A friend";
      const domain =
        req.hostname === "localhost" ? "localhost:3000" : req.hostname;
      const emailSent = await sendFriendInviteEmail(
        toEmail,
        fromUserName,
        invite.inviteToken,
        domain
      );

      res.json({
        ...invite,
        message: emailSent
          ? "Friend invite sent successfully!"
          : "Invite created but email delivery failed",
        emailSent,
      });
    } catch (error) {
      console.error("Error sending friend invite:", error);
      res.status(500).json({ message: "Failed to send friend invite" });
    }
  });

  app.get("/api/friends/invite/:inviteToken", async (req, res) => {
    try {
      const { inviteToken } = req.params;
      const [invite] = await db
        .select({
          id: friendInvites.id,
          fromUserId: friendInvites.fromUserId,
          toEmail: friendInvites.toEmail,
          status: friendInvites.status,
          expiresAt: friendInvites.expiresAt,
          fromUser: {
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
            profileImageUrl: users.profileImageUrl,
          },
        })
        .from(friendInvites)
        .innerJoin(users, eq(friendInvites.fromUserId, users.id))
        .where(eq(friendInvites.inviteToken, inviteToken))
        .limit(1);

      if (!invite) {
        return res.status(404).json({ message: "Invite not found" });
      }

      if (invite.status !== "pending" || invite.expiresAt < new Date()) {
        return res
          .status(400)
          .json({ message: "Invite expired or already used" });
      }

      res.json(invite);
    } catch (error) {
      console.error("Error fetching friend invite:", error);
      res.status(500).json({ message: "Failed to fetch friend invite" });
    }
  });

  app.post(
    "/api/friends/invite/accept",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { inviteToken } = req.body;
        console.log(
          "Accepting friend invite:",
          inviteToken,
          "by user:",
          userId
        );

        const friendship = await storage.acceptFriendInvite(
          inviteToken,
          userId
        );
        console.log("Friend invite accepted:", friendship);

        res.json(friendship);
      } catch (error) {
        console.error("Error accepting friend invite:", error);
        res.status(500).json({ message: "Failed to accept friend invite" });
      }
    }
  );

  app.get("/api/friends/invites", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const invites = await storage.getFriendInvites(userId);
      res.json(invites);
    } catch (error) {
      console.error("Error fetching friend invites:", error);
      res.status(500).json({ message: "Failed to fetch friend invites" });
    }
  });

  // Cancel friend invite
  app.delete(
    "/api/friends/invite/:inviteId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const inviteId = parseInt(req.params.inviteId);

        console.log("Canceling friend invite:", inviteId, "by user:", userId);

        const success = await storage.cancelFriendInvite(inviteId, userId);
        if (success) {
          res.json({ success: true, message: "Invite canceled successfully" });
        } else {
          res
            .status(404)
            .json({ message: "Invite not found or cannot be canceled" });
        }
      } catch (error) {
        console.error("Error canceling friend invite:", error);
        res.status(500).json({ message: "Failed to cancel friend invite" });
      }
    }
  );

  // Contact form route
  app.post("/api/contact", async (req, res) => {
    try {
      const { name, email, subject, message, category } = req.body;

      // Basic validation
      if (!name || !email || !subject || !message) {
        return res.status(400).json({ message: "All fields are required" });
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: "Invalid email address" });
      }

      console.log("Sending contact form email:", {
        name,
        email,
        subject,
        category,
      });

      // Send email using SendGrid
      const { sendContactEmail } = await import("./email");
      const emailSent = await sendContactEmail({
        name,
        email,
        subject,
        message,
        category: category || "general",
      });

      if (emailSent) {
        res.json({
          success: true,
          message:
            "Your message has been sent successfully! We'll get back to you within 24 hours.",
        });
      } else {
        res.status(500).json({
          success: false,
          message:
            "Failed to send message. Please try again or email us <NAME_EMAIL>",
        });
      }
    } catch (error) {
      console.error("Error processing contact form:", error);
      res.status(500).json({
        success: false,
        message: "Failed to send message. Please try again later.",
      });
    }
  });

  // Learning path routes
  app.post("/api/learning-paths", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { planId } = req.body;
      console.log("Creating learning path for user:", userId, "plan:", planId);

      // Check if plan exists and user has access
      const userPlans = await storage.getUserLearningPlans(userId);
      const plan = userPlans.find((p) => p.id === planId);
      if (!plan) {
        return res
          .status(404)
          .json({ message: "Learning plan not found or access denied" });
      }

      // Check if learning path already exists
      const existingPath = await storage.getLearningPath(userId, planId);
      if (existingPath) {
        return res.json(existingPath);
      }

      const path = await storage.createLearningPath(userId, planId);
      console.log("Created learning path:", path);
      res.json(path);
    } catch (error) {
      console.error("Error creating learning path:", error);
      res.status(500).json({ message: "Failed to create learning path" });
    }
  });

  app.get(
    "/api/learning-paths/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        console.log(
          "Fetching learning path for user:",
          userId,
          "plan:",
          planId
        );

        const path = await storage.getLearningPath(userId, planId);
        if (!path) {
          console.log("No learning path found, returning 404");
          return res.status(404).json({ message: "Learning path not found" });
        }

        // Get path nodes
        const nodes = await storage.getPathNodes(path.id);
        console.log("Found path nodes:", nodes.length);

        const pathWithNodes = {
          ...path,
          nodes: nodes,
        };

        res.json(pathWithNodes);
      } catch (error) {
        console.error("Error fetching learning path:", error);
        res.status(500).json({ message: "Failed to fetch learning path" });
      }
    }
  );

  app.put(
    "/api/learning-paths/:pathId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const { pathData, completionPercentage } = req.body;
        const path = await storage.updateLearningPath(
          pathId,
          pathData,
          completionPercentage
        );
        res.json(path);
      } catch (error) {
        console.error("Error updating learning path:", error);
        res.status(500).json({ message: "Failed to update learning path" });
      }
    }
  );

  app.put(
    "/api/learning-paths/:pathId/notes",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const { notes } = req.body;
        const path = await storage.updateLearningPathNotes(pathId, notes);
        res.json(path);
      } catch (error) {
        console.error("Error updating learning path notes:", error);
        res
          .status(500)
          .json({ message: "Failed to update learning path notes" });
      }
    }
  );

  app.get(
    "/api/learning-paths/:pathId/nodes",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const nodes = await storage.getPathNodes(pathId);
        res.json(nodes);
      } catch (error) {
        console.error("Error fetching path nodes:", error);
        res.status(500).json({ message: "Failed to fetch path nodes" });
      }
    }
  );

  app.put(
    "/api/learning-paths/nodes/:nodeId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const nodeId = parseInt(req.params.nodeId);
        const { isCompleted } = req.body;
        const node = await storage.updatePathNode(nodeId, isCompleted);
        res.json(node);
      } catch (error) {
        console.error("Error updating path node:", error);
        res.status(500).json({ message: "Failed to update path node" });
      }
    }
  );

  // Study group routes
  app.post("/api/study-groups", isAuthenticated, async (req: any, res) => {
    try {
      const createdById = req.user.id;
      const groupData = { ...req.body, createdById };
      const group = await storage.createStudyGroup(groupData);
      res.json(group);
    } catch (error) {
      console.error("Error creating study group:", error);
      res.status(500).json({ message: "Failed to create study group" });
    }
  });

  app.get(
    "/api/study-groups/:groupId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const group = await storage.getStudyGroup(groupId);
        if (!group) {
          return res.status(404).json({ message: "Study group not found" });
        }
        res.json(group);
      } catch (error) {
        console.error("Error fetching study group:", error);
        res.status(500).json({ message: "Failed to fetch study group" });
      }
    }
  );

  app.get("/api/study-groups", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const groups = await storage.getUserStudyGroups(userId);
      res.json(groups);
    } catch (error) {
      console.error("Error fetching study groups:", error);
      res.status(500).json({ message: "Failed to fetch study groups" });
    }
  });

  app.post(
    "/api/study-groups/:groupId/join",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const userId = req.user.id;
        const member = await storage.joinStudyGroup(groupId, userId);
        res.json(member);
      } catch (error) {
        console.error("Error joining study group:", error);
        res.status(500).json({ message: "Failed to join study group" });
      }
    }
  );

  app.delete(
    "/api/study-groups/:groupId/leave",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const userId = req.user.id;
        await storage.leaveStudyGroup(groupId, userId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error leaving study group:", error);
        res.status(500).json({ message: "Failed to leave study group" });
      }
    }
  );

  app.get(
    "/api/study-groups/:groupId/members",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const members = await storage.getStudyGroupMembers(groupId);
        res.json(members);
      } catch (error) {
        console.error("Error fetching group members:", error);
        res.status(500).json({ message: "Failed to fetch group members" });
      }
    }
  );

  app.get(
    "/api/study-groups/:groupId/activities",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const activities = await storage.getGroupActivities(groupId);
        res.json(activities);
      } catch (error) {
        console.error("Error fetching group activities:", error);
        res.status(500).json({ message: "Failed to fetch group activities" });
      }
    }
  );

  // AI Difficulty Optimizer routes
  app.get(
    "/api/difficulty/analyze/:videoId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const videoId = parseInt(req.params.videoId);
        const analysis = await storage.analyzeVideoDifficulty(videoId);
        res.json(analysis);
      } catch (error) {
        console.error("Error analyzing video difficulty:", error);
        res.status(500).json({ message: "Failed to analyze video difficulty" });
      }
    }
  );

  app.get("/api/user/skill-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { subject } = req.query;
      const profiles = await storage.getUserSkillProfile(
        userId,
        subject as string
      );
      res.json(profiles);
    } catch (error) {
      console.error("Error fetching skill profile:", error);
      res.status(500).json({ message: "Failed to fetch skill profile" });
    }
  });

  app.post(
    "/api/user/skill-profile",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { subject, ...profileData } = req.body;
        const profile = await storage.updateUserSkillProfile(
          userId,
          subject,
          profileData
        );
        res.json(profile);
      } catch (error) {
        console.error("Error updating skill profile:", error);
        res.status(500).json({ message: "Failed to update skill profile" });
      }
    }
  );

  app.get(
    "/api/recommendations/optimized/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        const limit = parseInt(req.query.limit as string) || 10;

        const recommendations = await storage.getOptimizedVideoRecommendations(
          userId,
          planId,
          limit
        );
        res.json(recommendations);
      } catch (error) {
        console.error("Error fetching optimized recommendations:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch optimized recommendations" });
      }
    }
  );

  app.post(
    "/api/learning/adaptation",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const adaptationData = { ...req.body, userId };
        const adaptation = await storage.recordLearningAdaptation(
          adaptationData
        );
        res.json(adaptation);
      } catch (error) {
        console.error("Error recording learning adaptation:", error);
        res
          .status(500)
          .json({ message: "Failed to record learning adaptation" });
      }
    }
  );

  app.get(
    "/api/learning/progression/:subject",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const subject = req.params.subject;
        const path = await storage.getDifficultyProgressionPath(
          userId,
          subject
        );
        res.json(path);
      } catch (error) {
        console.error("Error fetching difficulty progression:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch difficulty progression" });
      }
    }
  );

  app.get(
    "/api/learning/struggle-analysis",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const analysis = await storage.analyzeUserStrugglePatterns(userId);
        res.json(analysis);
      } catch (error) {
        console.error("Error analyzing struggle patterns:", error);
        res
          .status(500)
          .json({ message: "Failed to analyze struggle patterns" });
      }
    }
  );

  app.get(
    "/api/learning/adaptive-insights",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const insights = await storage.getAdaptiveLearningInsights(userId);
        res.json(insights);
      } catch (error) {
        console.error("Error fetching adaptive insights:", error);
        res.status(500).json({ message: "Failed to fetch adaptive insights" });
      }
    }
  );

  // Common Playlist routes
  app.get("/api/common-playlist", isAuthenticated, async (req: any, res) => {
    try {
      const category = req.query.category as string;
      const playlist = await storage.getCommonPlaylist(category);
      res.json(playlist);
    } catch (error) {
      console.error("Error fetching common playlist:", error);
      res.status(500).json({ message: "Failed to fetch common playlist" });
    }
  });

  app.post("/api/common-playlist", isAuthenticated, async (req: any, res) => {
    try {
      const item = await storage.addToCommonPlaylist(req.body);
      res.json(item);
    } catch (error) {
      console.error("Error adding to common playlist:", error);
      res.status(500).json({ message: "Failed to add to common playlist" });
    }
  });

  app.get(
    "/api/user-playlist-selections",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const selections = await storage.getUserPlaylistSelections(userId);
        res.json(selections);
      } catch (error) {
        console.error("Error fetching user playlist selections:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch user playlist selections" });
      }
    }
  );

  app.post(
    "/api/user-playlist-selections",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const selection = await storage.addUserPlaylistSelection({
          ...req.body,
          userId,
        });
        res.json(selection);
      } catch (error) {
        console.error("Error adding user playlist selection:", error);
        res
          .status(500)
          .json({ message: "Failed to add user playlist selection" });
      }
    }
  );

  app.delete(
    "/api/user-playlist-selections/:playlistItemId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const playlistItemId = parseInt(req.params.playlistItemId);
        await storage.removeUserPlaylistSelection(userId, playlistItemId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error removing user playlist selection:", error);
        res
          .status(500)
          .json({ message: "Failed to remove user playlist selection" });
      }
    }
  );

  app.post(
    "/api/plans/from-playlist",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { title, playlistItemIds } = req.body;
        const plan = await storage.createPlanFromPlaylistItems(
          userId,
          title,
          playlistItemIds
        );
        res.json(plan);
      } catch (error) {
        console.error("Error creating plan from playlist:", error);
        res
          .status(500)
          .json({ message: "Failed to create plan from playlist" });
      }
    }
  );

  app.post(
    "/api/plans/:planId/add-playlist-item",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        const { playlistItemId } = req.body;
        await storage.addPlaylistItemToPlan(userId, playlistItemId, planId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error adding playlist item to plan:", error);
        res
          .status(500)
          .json({ message: "Failed to add playlist item to plan" });
      }
    }
  );

  const httpServer = createServer(app);
  return httpServer;
}
