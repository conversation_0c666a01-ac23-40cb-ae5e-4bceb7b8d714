import {
  users,
  learningPlans,
  videos,
  planVideos,
  videoProgress,
  achievements,
  friendships,
  friendInvites,
  learningPaths,
  pathNodes,
  studyGroups,
  studyGroupMembers,
  groupActivities,
  userSkillProfiles,
  videoDifficultyAnalysis,
  learningAdaptations,
  commonPlaylist,
  userPlaylistSelections,
  passwordResetTokens,
  type User,
  type UpsertUser,
  type LearningPlan,
  type InsertLearningPlan,
  type Video,
  type InsertVideo,
  type PlanVideo,
  type InsertPlanVideo,
  type VideoProgress,
  type InsertVideoProgress,
  type Achievement,
  type InsertAchievement,
  type Friendship,
  type InsertFriendship,
  type FriendInvite,
  type InsertFriendInvite,
  type LearningPath,
  type InsertLearningPath,
  type PathNode,
  type InsertPathNode,
  type StudyGroup,
  type InsertStudyGroup,
  type StudyGroupMember,
  type InsertStudyGroupMember,
  type GroupActivity,
  type InsertGroupActivity,
  type UserSkillProfile,
  type InsertUserSkillProfile,
  type VideoDifficultyAnalysis,
  type InsertVideoDifficultyAnalysis,
  type LearningAdaptation,
  type PasswordResetToken,
  type InsertPasswordResetToken,
  type InsertLearningAdaptation,
  type CommonPlaylistItem,
  type InsertCommonPlaylistItem,
  type UserPlaylistSelection,
  type InsertUserPlaylistSelection,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, sql, gte, lte } from "drizzle-orm";
import { randomUUID } from "crypto";

// Interface for storage operations
export interface IStorage {
  // User operations
  // (IMPORTANT) these user operations are mandatory for Replit Auth.
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;

  // Learning Plan operations
  createLearningPlan(plan: InsertLearningPlan): Promise<LearningPlan>;
  getUserLearningPlans(userId: string): Promise<LearningPlan[]>;
  getLearningPlan(id: number): Promise<LearningPlan | undefined>;
  getLearningPlanBySlug(slug: string): Promise<LearningPlan | undefined>;
  getLearningPlanByShareToken(
    shareToken: string
  ): Promise<LearningPlan | undefined>;
  updateLearningPlan(
    id: number,
    updates: Partial<InsertLearningPlan>
  ): Promise<LearningPlan>;
  deleteLearningPlan(id: number): Promise<void>;
  generateShareTokenForPlan(planId: number): Promise<string>;
  copyPlanToUser(shareToken: string, userId: string): Promise<LearningPlan>;

  // Plan privacy and sharing operations
  sharePlan(
    planId: number,
    sharedByUserId: string,
    sharedWithUserId: string,
    permissions: { canAddVideos?: boolean; canReshare?: boolean }
  ): Promise<void>;
  unsharePlan(planId: number, sharedWithUserId: string): Promise<void>;
  getPlanShares(planId: number): Promise<any[]>;
  getUserSharedPlans(userId: string): Promise<LearningPlan[]>;
  canUserAccessPlan(
    planId: number,
    userId: string
  ): Promise<{
    canView: boolean;
    canAddVideos: boolean;
    canShare: boolean;
    isOwner: boolean;
  }>;
  generatePlanSlug(title: string): Promise<string>;

  // Video operations
  createVideo(video: InsertVideo): Promise<Video>;
  getVideoByYoutubeId(youtubeId: string): Promise<Video | undefined>;
  getVideo(id: number): Promise<Video | undefined>;
  getAllVideos(): Promise<Video[]>;
  deleteVideo(id: number): Promise<void>;

  // Plan Video operations
  addVideoToPlan(planVideo: InsertPlanVideo): Promise<PlanVideo>;
  getPlanVideos(planId: number): Promise<(PlanVideo & { video: Video })[]>;
  removeVideoFromPlan(planId: number, videoId: number): Promise<void>;

  // Video Progress operations
  updateVideoProgress(progress: InsertVideoProgress): Promise<VideoProgress>;
  getUserVideoProgress(
    userId: string,
    videoId: number
  ): Promise<VideoProgress | undefined>;
  getUserProgressForPlan(
    userId: string,
    planId: number
  ): Promise<VideoProgress[]>;

  // Achievement operations
  createAchievement(achievement: InsertAchievement): Promise<Achievement>;
  getUserAchievements(userId: string): Promise<Achievement[]>;

  // Analytics operations
  getUserDailyStats(userId: string): Promise<{
    videosWatched: number;
    timeSpent: number;
    streakDays: number;
  }>;
  getUserWeeklyProgress(userId: string): Promise<{
    totalHours: number;
    completedVideos: number;
    dailyHours: number[];
  }>;
  getUserMonthlyStats(userId: string): Promise<{
    totalVideos: number;
    totalHours: number;
    completedPlans: number;
    averageSessionTime: number;
    topCategories: string[];
    learningStreak: number;
  }>;

  // Continue learning operations
  getUserIncompleteVideos(userId: string): Promise<any[]>;

  // Recommendation operations
  getPersonalizedRecommendations(
    userId: string,
    limit?: number
  ): Promise<any[]>;
  getUserWatchingPatterns(userId: string): Promise<{
    favoriteChannels: { channelTitle: string; watchCount: number }[];
    averageWatchTime: number;
    mostActiveHours: number[];
    totalVideosCompleted: number;
  }>;

  // AI Mood Board operations
  getMoodBasedRecommendations(
    userId: string,
    mood: string,
    timeContext: string,
    limit?: number
  ): Promise<any[]>;
  getUserMoodProfile(userId: string): Promise<any>;
  getAIInsights(userId: string): Promise<any>;

  // Friend system operations
  sendFriendRequest(
    requesterId: string,
    receiverId: string
  ): Promise<Friendship>;
  acceptFriendRequest(friendshipId: number): Promise<Friendship>;
  rejectFriendRequest(friendshipId: number): Promise<void>;
  getFriends(userId: string): Promise<(User & { friendshipId: number })[]>;
  getFriendRequests(
    userId: string
  ): Promise<(Friendship & { requester: User })[]>;
  unfriend(userId: string, friendId: string): Promise<void>;

  // Friend invite operations
  sendFriendInvite(fromUserId: string, toEmail: string): Promise<FriendInvite>;
  acceptFriendInvite(inviteToken: string, userId: string): Promise<Friendship>;
  getFriendInvites(userId: string): Promise<FriendInvite[]>;
  cancelFriendInvite(inviteId: number, userId: string): Promise<boolean>;

  // Learning path operations
  createLearningPath(userId: string, planId: number): Promise<LearningPath>;
  getLearningPath(
    userId: string,
    planId: number
  ): Promise<LearningPath | undefined>;
  updateLearningPath(
    pathId: number,
    pathData: any,
    completionPercentage: number
  ): Promise<LearningPath>;
  updateLearningPathNotes(pathId: number, notes: string): Promise<LearningPath>;
  getPathNodes(pathId: number): Promise<(PathNode & { video: Video })[]>;
  updatePathNode(nodeId: number, isCompleted: boolean): Promise<PathNode>;

  // Study group operations
  createStudyGroup(groupData: InsertStudyGroup): Promise<StudyGroup>;
  getStudyGroup(groupId: number): Promise<StudyGroup | undefined>;
  getUserStudyGroups(userId: string): Promise<StudyGroup[]>;
  joinStudyGroup(groupId: number, userId: string): Promise<StudyGroupMember>;
  leaveStudyGroup(groupId: number, userId: string): Promise<void>;
  getStudyGroupMembers(
    groupId: number
  ): Promise<(StudyGroupMember & { user: User })[]>;
  createGroupActivity(activity: InsertGroupActivity): Promise<GroupActivity>;
  getGroupActivities(
    groupId: number
  ): Promise<(GroupActivity & { user: User })[]>;

  // AI Difficulty Optimizer operations
  analyzeVideoDifficulty(videoId: number): Promise<VideoDifficultyAnalysis>;
  getUserSkillProfile(
    userId: string,
    subject?: string
  ): Promise<UserSkillProfile[]>;
  updateUserSkillProfile(
    userId: string,
    subject: string,
    data: Partial<InsertUserSkillProfile>
  ): Promise<UserSkillProfile>;
  getOptimizedVideoRecommendations(
    userId: string,
    planId: number,
    limit?: number
  ): Promise<any[]>;
  recordLearningAdaptation(
    adaptation: InsertLearningAdaptation
  ): Promise<LearningAdaptation>;
  getDifficultyProgressionPath(userId: string, subject: string): Promise<any[]>;
  analyzeUserStrugglePatterns(userId: string): Promise<any>;
  getAdaptiveLearningInsights(userId: string): Promise<any>;

  // Common Playlist operations
  getCommonPlaylist(category?: string): Promise<CommonPlaylistItem[]>;
  addToCommonPlaylist(
    item: InsertCommonPlaylistItem
  ): Promise<CommonPlaylistItem>;
  getUserPlaylistSelections(userId: string): Promise<UserPlaylistSelection[]>;
  addUserPlaylistSelection(
    selection: InsertUserPlaylistSelection
  ): Promise<UserPlaylistSelection>;
  removeUserPlaylistSelection(
    userId: string,
    playlistItemId: number
  ): Promise<void>;
  createPlanFromPlaylistItems(
    userId: string,
    title: string,
    playlistItemIds: number[]
  ): Promise<LearningPlan>;
  addPlaylistItemToPlan(
    userId: string,
    playlistItemId: number,
    planId: number
  ): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  // (IMPORTANT) these user operations are mandatory for Replit Auth.
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  // Password Reset operations
  async createPasswordResetToken(
    tokenData: InsertPasswordResetToken
  ): Promise<PasswordResetToken> {
    const [token] = await db
      .insert(passwordResetTokens)
      .values(tokenData)
      .returning();
    return token;
  }

  async getPasswordResetToken(
    token: string
  ): Promise<PasswordResetToken | undefined> {
    const [resetToken] = await db
      .select()
      .from(passwordResetTokens)
      .where(eq(passwordResetTokens.token, token));
    return resetToken;
  }

  async markPasswordResetTokenAsUsed(token: string): Promise<void> {
    await db
      .update(passwordResetTokens)
      .set({ isUsed: true })
      .where(eq(passwordResetTokens.token, token));
  }

  async updateUserPassword(
    userId: string,
    passwordHash: string
  ): Promise<void> {
    await db
      .update(users)
      .set({ passwordHash, updatedAt: new Date() })
      .where(eq(users.id, userId));
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  // Learning Plan operations
  async createLearningPlan(plan: InsertLearningPlan): Promise<LearningPlan> {
    const [learningPlan] = await db
      .insert(learningPlans)
      .values(plan)
      .returning();
    return learningPlan;
  }

  async getUserLearningPlans(userId: string): Promise<LearningPlan[]> {
    return await db
      .select()
      .from(learningPlans)
      .where(
        and(eq(learningPlans.userId, userId), eq(learningPlans.isActive, true))
      )
      .orderBy(desc(learningPlans.updatedAt));
  }

  async getLearningPlan(id: number): Promise<LearningPlan | undefined> {
    const [plan] = await db
      .select()
      .from(learningPlans)
      .where(eq(learningPlans.id, id));
    return plan;
  }

  async getLearningPlanBySlug(slug: string): Promise<LearningPlan | undefined> {
    const [plan] = await db
      .select()
      .from(learningPlans)
      .where(eq(learningPlans.slug, slug));
    return plan;
  }

  // Import and use the plan privacy methods
  async generatePlanSlug(title: string): Promise<string> {
    const { generatePlanSlug } = await import("./plan-privacy-methods");
    return generatePlanSlug(title);
  }

  async sharePlan(
    planId: number,
    sharedByUserId: string,
    sharedWithUserId: string,
    permissions: { canAddVideos?: boolean; canReshare?: boolean }
  ): Promise<void> {
    const { sharePlan } = await import("./plan-privacy-methods");
    return sharePlan(planId, sharedByUserId, sharedWithUserId, permissions);
  }

  async unsharePlan(planId: number, sharedWithUserId: string): Promise<void> {
    const { unsharePlan } = await import("./plan-privacy-methods");
    return unsharePlan(planId, sharedWithUserId);
  }

  async getPlanShares(planId: number): Promise<any[]> {
    const { getPlanShares } = await import("./plan-privacy-methods");
    return getPlanShares(planId);
  }

  async getUserSharedPlans(userId: string): Promise<any[]> {
    const { getUserSharedPlans } = await import("./plan-privacy-methods");
    return getUserSharedPlans(userId);
  }

  async canUserAccessPlan(
    planId: number,
    userId: string
  ): Promise<{
    canView: boolean;
    canAddVideos: boolean;
    canShare: boolean;
    isOwner: boolean;
  }> {
    const { canUserAccessPlan } = await import("./plan-privacy-methods");
    return canUserAccessPlan(planId, userId);
  }

  async updateLearningPlan(
    id: number,
    updates: Partial<InsertLearningPlan>
  ): Promise<LearningPlan> {
    const [plan] = await db
      .update(learningPlans)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(learningPlans.id, id))
      .returning();
    return plan;
  }

  async deleteLearningPlan(id: number): Promise<void> {
    await db
      .update(learningPlans)
      .set({ isActive: false, updatedAt: new Date() })
      .where(eq(learningPlans.id, id));
  }

  async getLearningPlanByShareToken(
    shareToken: string
  ): Promise<LearningPlan | undefined> {
    const [plan] = await db
      .select()
      .from(learningPlans)
      .where(eq(learningPlans.shareToken, shareToken));
    return plan;
  }

  async generateShareTokenForPlan(planId: number): Promise<string> {
    const shareToken = randomUUID();
    await db
      .update(learningPlans)
      .set({ shareToken, isPublic: true })
      .where(eq(learningPlans.id, planId));
    return shareToken;
  }

  async copyPlanToUser(
    shareToken: string,
    userId: string
  ): Promise<LearningPlan> {
    // Get the original plan
    const originalPlan = await this.getLearningPlanByShareToken(shareToken);
    if (!originalPlan) {
      throw new Error("Plan not found");
    }

    // Create a copy for the new user
    const newPlan = await this.createLearningPlan({
      userId,
      title: `${originalPlan.title} (Copy)`,
      description: originalPlan.description,
      isActive: true,
    });

    // Copy all videos from the original plan
    const originalVideos = await this.getPlanVideos(originalPlan.id);
    for (const planVideo of originalVideos) {
      await this.addVideoToPlan({
        planId: newPlan.id,
        videoId: planVideo.video.id,
        orderIndex: planVideo.orderIndex,
      });
    }

    return newPlan;
  }

  // Video operations
  async createVideo(video: InsertVideo): Promise<Video> {
    const [newVideo] = await db.insert(videos).values(video).returning();
    return newVideo;
  }

  async getVideoByYoutubeId(youtubeId: string): Promise<Video | undefined> {
    const [video] = await db
      .select()
      .from(videos)
      .where(eq(videos.youtubeId, youtubeId));
    return video;
  }

  async getVideo(id: number): Promise<Video | undefined> {
    const [video] = await db.select().from(videos).where(eq(videos.id, id));
    return video;
  }

  async getAllVideos(): Promise<Video[]> {
    return await db.select().from(videos).limit(50);
  }

  async deleteVideo(id: number): Promise<void> {
    // First remove video from all plans
    await db.delete(planVideos).where(eq(planVideos.videoId, id));

    // Remove video progress entries
    await db.delete(videoProgress).where(eq(videoProgress.videoId, id));

    // Finally remove the video itself
    await db.delete(videos).where(eq(videos.id, id));
  }

  // Plan Video operations
  async addVideoToPlan(planVideo: InsertPlanVideo): Promise<PlanVideo> {
    console.log("Adding video to plan with data:", planVideo);

    // Ensure orderIndex is set
    let orderIndex = planVideo.orderIndex;
    if (orderIndex === undefined) {
      const existingVideos = await this.getPlanVideos(planVideo.planId);
      orderIndex = existingVideos.length;
    }

    const [newPlanVideo] = await db
      .insert(planVideos)
      .values({
        planId: planVideo.planId,
        videoId: planVideo.videoId,
        orderIndex: orderIndex,
      })
      .returning();

    console.log("Successfully added video to plan:", newPlanVideo);
    return newPlanVideo;
  }

  async getPlanVideos(
    planId: number
  ): Promise<(PlanVideo & { video: Video })[]> {
    return await db
      .select({
        id: planVideos.id,
        planId: planVideos.planId,
        videoId: planVideos.videoId,
        orderIndex: planVideos.orderIndex,
        addedAt: planVideos.addedAt,
        video: videos,
      })
      .from(planVideos)
      .innerJoin(videos, eq(planVideos.videoId, videos.id))
      .where(eq(planVideos.planId, planId))
      .orderBy(planVideos.orderIndex);
  }

  async removeVideoFromPlan(planId: number, videoId: number): Promise<void> {
    await db
      .delete(planVideos)
      .where(
        and(eq(planVideos.planId, planId), eq(planVideos.videoId, videoId))
      );
  }

  // Video Progress operations
  async updateVideoProgress(
    progress: InsertVideoProgress
  ): Promise<VideoProgress> {
    console.log("Storage: updateVideoProgress called with:", progress);

    try {
      // First try to find existing progress
      const existing = await db
        .select()
        .from(videoProgress)
        .where(
          and(
            eq(videoProgress.userId, progress.userId),
            eq(videoProgress.videoId, progress.videoId)
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Update existing record
        const [result] = await db
          .update(videoProgress)
          .set({
            currentTime: progress.currentTime,
            isCompleted: progress.isCompleted,
            lastWatched: new Date(),
          })
          .where(
            and(
              eq(videoProgress.userId, progress.userId),
              eq(videoProgress.videoId, progress.videoId)
            )
          )
          .returning();

        console.log(
          "Storage: updateVideoProgress successful (updated):",
          result
        );
        return result;
      } else {
        // Insert new record
        const [result] = await db
          .insert(videoProgress)
          .values({ ...progress, lastWatched: new Date() })
          .returning();

        console.log(
          "Storage: updateVideoProgress successful (inserted):",
          result
        );
        return result;
      }
    } catch (error) {
      console.error("Storage: updateVideoProgress error:", error);
      throw error;
    }
  }

  private get videoProgress() {
    return videoProgress;
  }

  async getUserVideoProgress(
    userId: string,
    videoId: number
  ): Promise<VideoProgress | undefined> {
    const [progress] = await db
      .select()
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.videoId, videoId)
        )
      );
    return progress;
  }

  async getUserProgressForPlan(
    userId: string,
    planId: number
  ): Promise<VideoProgress[]> {
    return await db
      .select({
        id: videoProgress.id,
        userId: videoProgress.userId,
        videoId: videoProgress.videoId,
        currentTime: videoProgress.currentTime,
        isCompleted: videoProgress.isCompleted,
        lastWatched: videoProgress.lastWatched,
      })
      .from(videoProgress)
      .innerJoin(planVideos, eq(videoProgress.videoId, planVideos.videoId))
      .where(
        and(eq(videoProgress.userId, userId), eq(planVideos.planId, planId))
      );
  }

  // Achievement operations
  async createAchievement(
    achievement: InsertAchievement
  ): Promise<Achievement> {
    const [newAchievement] = await db
      .insert(achievements)
      .values(achievement)
      .returning();
    return newAchievement;
  }

  async getUserAchievements(userId: string): Promise<Achievement[]> {
    return await db
      .select()
      .from(achievements)
      .where(eq(achievements.userId, userId))
      .orderBy(desc(achievements.earnedAt));
  }

  // Analytics operations
  async getUserMonthlyStats(userId: string): Promise<{
    totalVideos: number;
    totalHours: number;
    completedPlans: number;
    averageSessionTime: number;
    topCategories: string[];
    learningStreak: number;
  }> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get total completed videos this month
    const completedVideosResult = await db
      .select({ count: sql<number>`count(*)::int` })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true),
          sql`${videoProgress.lastWatched} >= ${startOfMonth}`
        )
      );

    const totalVideos = completedVideosResult[0]?.count || 0;

    // Get total hours watched this month
    const totalTimeResult = await db
      .select({
        totalTime: sql<number>`sum(${videoProgress.currentTime})::int`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          sql`${videoProgress.lastWatched} >= ${startOfMonth}`
        )
      );

    const totalSeconds = totalTimeResult[0]?.totalTime || 0;
    const totalHours = Math.round((totalSeconds / 3600) * 100) / 100;

    // Get completed learning plans this month
    const completedPlansResult = await db
      .select({
        planId: planVideos.planId,
        totalVideos: sql<number>`count(*)::int`,
        completedVideos: sql<number>`sum(case when ${videoProgress.isCompleted} then 1 else 0 end)::int`,
      })
      .from(planVideos)
      .leftJoin(
        videoProgress,
        and(
          eq(planVideos.videoId, videoProgress.videoId),
          eq(videoProgress.userId, userId)
        )
      )
      .innerJoin(learningPlans, eq(planVideos.planId, learningPlans.id))
      .where(eq(learningPlans.userId, userId))
      .groupBy(planVideos.planId)
      .having(
        sql`count(*) = sum(case when ${videoProgress.isCompleted} then 1 else 0 end)`
      );

    const completedPlans = completedPlansResult.length;

    // Calculate average session time (simplified)
    const averageSessionTime = totalVideos > 0 ? totalHours / totalVideos : 0;

    // Get top categories (based on channel titles for now)
    const topChannelsResult = await db
      .select({
        channelTitle: videos.channelTitle,
        count: sql<number>`count(*)::int`,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true),
          sql`${videoProgress.lastWatched} >= ${startOfMonth}`
        )
      )
      .groupBy(videos.channelTitle)
      .orderBy(sql`count(*) desc`)
      .limit(3);

    const topCategories = topChannelsResult
      .map((r) => r.channelTitle)
      .filter((title): title is string => title !== null);

    // Calculate learning streak (days with completed videos)
    const streakResult = await db
      .select({
        date: sql<string>`date(${videoProgress.lastWatched})`,
        count: sql<number>`count(*)::int`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true)
        )
      )
      .groupBy(sql`date(${videoProgress.lastWatched})`)
      .orderBy(sql`date(${videoProgress.lastWatched}) desc`)
      .limit(30);

    // Calculate consecutive days
    let learningStreak = 0;
    const today = new Date();
    const dates = streakResult.map((r) => new Date(r.date));

    for (let i = 0; i < dates.length; i++) {
      const diffDays = Math.floor(
        (today.getTime() - dates[i].getTime()) / (1000 * 60 * 60 * 24)
      );
      if (diffDays === learningStreak) {
        learningStreak++;
      } else {
        break;
      }
    }

    return {
      totalVideos,
      totalHours,
      completedPlans,
      averageSessionTime: Math.round(averageSessionTime * 100) / 100,
      topCategories,
      learningStreak,
    };
  }

  async getUserDailyStats(userId: string): Promise<{
    videosWatched: number;
    timeSpent: number;
    streakDays: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [dailyStats] = await db
      .select({
        videosWatched: sql<number>`count(distinct ${videoProgress.videoId})`,
        timeSpent: sql<number>`coalesce(sum(${videoProgress.currentTime}), 0)`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          sql`${videoProgress.lastWatched} >= ${today}`
        )
      );

    // Calculate streak days (simplified)
    const [streak] = await db
      .select({
        count: sql<number>`count(distinct date(${videoProgress.lastWatched}))`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          sql`${videoProgress.lastWatched} >= ${new Date(
            Date.now() - 30 * 24 * 60 * 60 * 1000
          )}`
        )
      );

    return {
      videosWatched: dailyStats?.videosWatched || 0,
      timeSpent: Math.round((dailyStats?.timeSpent || 0) / 3600), // Convert to hours
      streakDays: streak?.count || 0,
    };
  }

  async getUserWeeklyProgress(userId: string): Promise<{
    totalHours: number;
    completedVideos: number;
    dailyHours: number[];
  }> {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const [weeklyStats] = await db
      .select({
        totalHours: sql<number>`coalesce(sum(${videoProgress.currentTime}), 0) / 3600`,
        completedVideos: sql<number>`count(*) filter (where ${videoProgress.isCompleted} = true)`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          sql`${videoProgress.lastWatched} >= ${weekAgo}`
        )
      );

    // Get daily hours for the past 7 days (simplified)
    const dailyHours = new Array(7).fill(0);

    return {
      totalHours: Math.round(weeklyStats?.totalHours || 0),
      completedVideos: weeklyStats?.completedVideos || 0,
      dailyHours,
    };
  }

  // Continue learning operations
  async getUserIncompleteVideos(userId: string): Promise<any[]> {
    const result = await db
      .select({
        id: videos.id,
        youtubeId: videos.youtubeId,
        title: videos.title,
        description: videos.description,
        thumbnailUrl: videos.thumbnailUrl,
        duration: videos.duration,
        channelTitle: videos.channelTitle,
        progress: {
          currentTime: videoProgress.currentTime,
          isCompleted: videoProgress.isCompleted,
          progressPercentage: sql<number>`(${videoProgress.currentTime} / 100.0) * 100`,
        },
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, false),
          sql`${videoProgress.currentTime} > 0`
        )
      )
      .orderBy(desc(videoProgress.lastWatched))
      .limit(10);

    return result;
  }

  // Recommendation operations
  async getPersonalizedRecommendations(
    userId: string,
    limit: number = 10
  ): Promise<any[]> {
    // Get user's completed videos to analyze preferences
    const completedVideos = await db
      .select({
        videoId: videoProgress.videoId,
        channelTitle: videos.channelTitle,
        title: videos.title,
        lastWatched: videoProgress.lastWatched,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true)
        )
      )
      .orderBy(desc(videoProgress.lastWatched));

    if (completedVideos.length === 0) {
      // If no history, return popular videos
      return await db
        .select({
          id: videos.id,
          youtubeId: videos.youtubeId,
          title: videos.title,
          description: videos.description,
          thumbnailUrl: videos.thumbnailUrl,
          duration: videos.duration,
          channelTitle: videos.channelTitle,
          viewCount: videos.viewCount,
          publishedAt: videos.publishedAt,
        })
        .from(videos)
        .orderBy(desc(videos.viewCount))
        .limit(limit);
    }

    // Get favorite channels (most watched)
    const favoriteChannels = await db
      .select({
        channelTitle: videos.channelTitle,
        count: sql<number>`count(*)`,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true)
        )
      )
      .groupBy(videos.channelTitle)
      .orderBy(sql`count(*) desc`)
      .limit(5);

    const channelNames = favoriteChannels
      .map((c) => c.channelTitle)
      .filter(Boolean);

    // Get watched video IDs to exclude from recommendations
    const watchedVideoIds = await db
      .select({ videoId: videoProgress.videoId })
      .from(videoProgress)
      .where(eq(videoProgress.userId, userId));

    const excludeIds = watchedVideoIds.map((w) => w.videoId);

    // Get recommendations from favorite channels
    let recommendations: any[] = [];

    if (channelNames.length > 0) {
      recommendations = await db
        .select({
          id: videos.id,
          youtubeId: videos.youtubeId,
          title: videos.title,
          description: videos.description,
          thumbnailUrl: videos.thumbnailUrl,
          duration: videos.duration,
          channelTitle: videos.channelTitle,
          viewCount: videos.viewCount,
          publishedAt: videos.publishedAt,
          relevanceScore: sql<number>`3`, // High score for favorite channels
        })
        .from(videos)
        .where(
          and(
            channelNames.length > 0
              ? sql`${videos.channelTitle} IN (${channelNames
                  .map((name) => `'${name.replace(/'/g, "''")}'`)
                  .join(",")})`
              : sql`1=1`,
            excludeIds.length > 0
              ? sql`${videos.id} NOT IN (${excludeIds.join(",")})`
              : sql`1=1`
          )
        )
        .orderBy(desc(videos.viewCount))
        .limit(Math.floor(limit * 0.7)); // 70% from favorite channels
    }

    // Fill remaining slots with popular videos
    const remainingSlots = limit - recommendations.length;
    if (remainingSlots > 0) {
      const popularVideos = await db
        .select({
          id: videos.id,
          youtubeId: videos.youtubeId,
          title: videos.title,
          description: videos.description,
          thumbnailUrl: videos.thumbnailUrl,
          duration: videos.duration,
          channelTitle: videos.channelTitle,
          viewCount: videos.viewCount,
          publishedAt: videos.publishedAt,
          relevanceScore: sql<number>`1`, // Lower score for general popular videos
        })
        .from(videos)
        .where(
          excludeIds.length > 0
            ? sql`${videos.id} NOT IN (${excludeIds.join(",")})`
            : sql`1=1`
        )
        .orderBy(desc(videos.viewCount))
        .limit(remainingSlots);

      recommendations = [...recommendations, ...popularVideos];
    }

    return recommendations;
  }

  async getUserWatchingPatterns(userId: string): Promise<{
    favoriteChannels: { channelTitle: string; watchCount: number }[];
    averageWatchTime: number;
    mostActiveHours: number[];
    totalVideosCompleted: number;
  }> {
    // Get favorite channels
    const favoriteChannels = await db
      .select({
        channelTitle: videos.channelTitle,
        watchCount: sql<number>`count(*)`,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true)
        )
      )
      .groupBy(videos.channelTitle)
      .orderBy(sql`count(*) desc`)
      .limit(5);

    // Get average watch time and total completed
    const [stats] = await db
      .select({
        averageWatchTime: sql<number>`coalesce(avg(${videoProgress.currentTime}), 0)`,
        totalCompleted: sql<number>`count(*)`,
      })
      .from(videoProgress)
      .where(
        and(
          eq(videoProgress.userId, userId),
          eq(videoProgress.isCompleted, true)
        )
      );

    // Get most active hours
    const hourlyActivity = await db
      .select({
        hour: sql<number>`extract(hour from ${videoProgress.lastWatched})`,
        count: sql<number>`count(*)`,
      })
      .from(videoProgress)
      .where(eq(videoProgress.userId, userId))
      .groupBy(sql`extract(hour from ${videoProgress.lastWatched})`)
      .orderBy(sql`count(*) desc`)
      .limit(3);

    return {
      favoriteChannels: favoriteChannels.map((fc) => ({
        channelTitle: fc.channelTitle || "",
        watchCount: fc.watchCount,
      })),
      averageWatchTime: Math.round(stats?.averageWatchTime || 0),
      mostActiveHours: hourlyActivity
        .map((ha) => ha.hour)
        .filter((h) => h !== null),
      totalVideosCompleted: stats?.totalCompleted || 0,
    };
  }

  // AI Mood Board operations
  async getMoodBasedRecommendations(
    userId: string,
    mood: string,
    timeContext: string,
    limit: number = 10
  ): Promise<any[]> {
    // Define mood keywords and filters
    const moodKeywords: Record<string, string[]> = {
      focused: [
        "tutorial",
        "course",
        "lesson",
        "guide",
        "learn",
        "deep",
        "advanced",
      ],
      creative: [
        "design",
        "art",
        "creative",
        "inspiration",
        "innovation",
        "diy",
        "craft",
      ],
      motivational: [
        "motivation",
        "success",
        "inspiration",
        "goals",
        "achievement",
        "mindset",
      ],
      relaxing: [
        "basics",
        "introduction",
        "overview",
        "gentle",
        "easy",
        "beginner",
      ],
      trending: [
        "trending",
        "latest",
        "new",
        "2024",
        "2025",
        "recent",
        "popular",
      ],
      discovery: [
        "introduction",
        "beginner",
        "explore",
        "discover",
        "new",
        "overview",
      ],
    };

    // Get user's watch history for personalization
    const userHistory = await db
      .select({
        videoId: videoProgress.videoId,
        channelTitle: videos.channelTitle,
        title: videos.title,
        isCompleted: videoProgress.isCompleted,
        currentTime: videoProgress.currentTime,
        lastWatched: videoProgress.lastWatched,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(eq(videoProgress.userId, userId))
      .orderBy(desc(videoProgress.lastWatched))
      .limit(50);

    // Get watched video IDs to exclude
    const watchedIds = userHistory.map((h) => h.videoId);

    // Calculate mood scores based on keywords
    const keywords = moodKeywords[mood] || [];

    // Get base recommendations
    let baseQuery = db
      .select({
        id: videos.id,
        youtubeId: videos.youtubeId,
        title: videos.title,
        description: videos.description,
        thumbnailUrl: videos.thumbnailUrl,
        duration: videos.duration,
        channelTitle: videos.channelTitle,
        viewCount: videos.viewCount,
        publishedAt: videos.publishedAt,
      })
      .from(videos);

    // Exclude watched videos
    if (watchedIds.length > 0) {
      baseQuery = baseQuery.where(
        sql`${videos.id} NOT IN (${watchedIds.join(",")})`
      );
    }

    const baseVideos = await baseQuery
      .orderBy(desc(videos.viewCount))
      .limit(limit * 3); // Get more to filter

    // Score videos based on mood and user patterns
    const scoredVideos = baseVideos.map((video) => {
      let moodScore = 0;
      const titleLower = video.title.toLowerCase();
      const descLower = video.description.toLowerCase();

      // Mood keyword matching
      keywords.forEach((keyword) => {
        if (titleLower.includes(keyword.toLowerCase())) moodScore += 3;
        if (descLower.includes(keyword.toLowerCase())) moodScore += 1;
      });

      // User preference scoring (favorite channels)
      const favoriteChannels = userHistory
        .filter((h) => h.isCompleted)
        .reduce((acc, h) => {
          acc[h.channelTitle] = (acc[h.channelTitle] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

      if (favoriteChannels[video.channelTitle]) {
        moodScore += Math.min(favoriteChannels[video.channelTitle] * 2, 10);
      }

      // Time context scoring
      const timeBonus = this.getTimeContextBonus(timeContext, video);
      moodScore += timeBonus;

      // Normalize mood score
      const normalizedScore = Math.min(moodScore / 20, 1);

      return {
        ...video,
        moodScore: normalizedScore,
        relevanceScore: moodScore,
        category: mood,
        tags: keywords.filter(
          (k) =>
            titleLower.includes(k.toLowerCase()) ||
            descLower.includes(k.toLowerCase())
        ),
      };
    });

    // Sort by mood score and return top results
    return scoredVideos
      .sort((a, b) => b.moodScore - a.moodScore)
      .slice(0, limit);
  }

  private getTimeContextBonus(timeContext: string, video: any): number {
    const titleLower = video.title.toLowerCase();
    const duration = video.duration || "";

    switch (timeContext) {
      case "morning":
        // Prefer shorter, energizing content
        if (titleLower.includes("quick") || titleLower.includes("morning"))
          return 5;
        if (duration.includes("5:") || duration.includes("10:")) return 3;
        return 0;

      case "afternoon":
        // Prefer comprehensive, focused content
        if (titleLower.includes("deep") || titleLower.includes("complete"))
          return 5;
        if (titleLower.includes("tutorial") || titleLower.includes("course"))
          return 3;
        return 0;

      case "evening":
        // Prefer relaxing, review content
        if (titleLower.includes("review") || titleLower.includes("summary"))
          return 5;
        if (titleLower.includes("basics") || titleLower.includes("intro"))
          return 3;
        return 0;

      case "late":
        // Prefer short, light content
        if (titleLower.includes("tips") || titleLower.includes("quick"))
          return 5;
        if (duration.includes("5:") || duration.includes("3:")) return 3;
        return 0;

      default:
        return 0;
    }
  }

  async getUserMoodProfile(userId: string): Promise<any> {
    // Get completion rate
    const progressStats = await db
      .select({
        total: sql<number>`count(*)`,
        completed: sql<number>`sum(case when ${videoProgress.isCompleted} then 1 else 0 end)`,
        avgWatchTime: sql<number>`avg(${videoProgress.currentTime})`,
      })
      .from(videoProgress)
      .where(eq(videoProgress.userId, userId));

    const stats = progressStats[0];
    const completionRate =
      stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

    // Get recent activity
    const recentActivity = await db
      .select({
        title: videos.title,
        channelTitle: videos.channelTitle,
        lastWatched: videoProgress.lastWatched,
      })
      .from(videoProgress)
      .innerJoin(videos, eq(videoProgress.videoId, videos.id))
      .where(eq(videoProgress.userId, userId))
      .orderBy(desc(videoProgress.lastWatched))
      .limit(10);

    // Determine skill level based on completion rate and watch patterns
    let skillLevel = "Beginner";
    if (completionRate > 70) skillLevel = "Advanced";
    else if (completionRate > 40) skillLevel = "Intermediate";

    // Extract interests from recent activity
    const interests = [
      ...new Set(recentActivity.map((a) => a.channelTitle).filter(Boolean)),
    ].slice(0, 5);

    return {
      currentMood: "focused", // Default, could be enhanced with ML
      preferredLearningTime: this.getPreferredLearningTime(recentActivity),
      recentActivity: recentActivity.map((a) => a.title),
      skillLevel,
      interests,
      completionRate,
      avgWatchTime: Math.round(stats.avgWatchTime || 0),
    };
  }

  private getPreferredLearningTime(recentActivity: any[]): string {
    const hourCounts = recentActivity.reduce((acc, activity) => {
      const hour = new Date(activity.lastWatched).getHours();
      const timeSlot =
        hour < 12 ? "morning" : hour < 17 ? "afternoon" : "evening";
      acc[timeSlot] = (acc[timeSlot] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return (
      Object.entries(hourCounts).sort(([, a], [, b]) => b - a)[0]?.[0] ||
      "morning"
    );
  }

  async getAIInsights(userId: string): Promise<any> {
    const patterns = await this.getUserWatchingPatterns(userId);
    const moodProfile = await this.getUserMoodProfile(userId);

    // Determine best learning time
    const timePreferences = {
      morning: patterns.mostActiveHours.filter((h) => h >= 6 && h < 12).length,
      afternoon: patterns.mostActiveHours.filter((h) => h >= 12 && h < 17)
        .length,
      evening: patterns.mostActiveHours.filter((h) => h >= 17 && h < 22).length,
    };

    const bestTime =
      Object.entries(timePreferences).sort(([, a], [, b]) => b - a)[0]?.[0] ||
      "morning";

    // Determine preferred learning style
    const avgWatchTime = patterns.averageWatchTime;
    let preferredStyle = "bite-sized";
    if (avgWatchTime > 30) preferredStyle = "comprehensive";
    else if (avgWatchTime > 15) preferredStyle = "moderate-length";

    return {
      bestTime: bestTime,
      preferredStyle: preferredStyle,
      completionRate: moodProfile.completionRate,
      totalVideosWatched: patterns.totalVideosCompleted,
      favoriteChannels: patterns.favoriteChannels.slice(0, 3),
      recommendation: this.generatePersonalizedRecommendation(
        moodProfile,
        patterns
      ),
    };
  }

  private generatePersonalizedRecommendation(
    moodProfile: any,
    patterns: any
  ): string {
    if (moodProfile.completionRate > 80) {
      return "You're an excellent learner! Try exploring advanced topics in your favorite subjects.";
    } else if (moodProfile.completionRate > 50) {
      return "You're making good progress! Consider setting specific learning goals to boost completion.";
    } else {
      return "Start with shorter videos to build momentum, then gradually work up to longer content.";
    }
  }

  // Friend system operations
  async sendFriendRequest(
    requesterId: string,
    receiverId: string
  ): Promise<Friendship> {
    const [friendship] = await db
      .insert(friendships)
      .values({
        requesterId,
        receiverId,
        status: "pending",
      })
      .returning();
    return friendship;
  }

  async acceptFriendRequest(friendshipId: number): Promise<Friendship> {
    const [friendship] = await db
      .update(friendships)
      .set({ status: "accepted", updatedAt: new Date() })
      .where(eq(friendships.id, friendshipId))
      .returning();
    return friendship;
  }

  async rejectFriendRequest(friendshipId: number): Promise<void> {
    await db.delete(friendships).where(eq(friendships.id, friendshipId));
  }

  async getFriends(
    userId: string
  ): Promise<(User & { friendshipId: number })[]> {
    const friendsList = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        profileImageUrl: users.profileImageUrl,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        friendshipId: friendships.id,
      })
      .from(friendships)
      .innerJoin(
        users,
        or(
          and(
            eq(friendships.requesterId, userId),
            eq(users.id, friendships.receiverId)
          ),
          and(
            eq(friendships.receiverId, userId),
            eq(users.id, friendships.requesterId)
          )
        )
      )
      .where(eq(friendships.status, "accepted"));

    return friendsList;
  }

  async getFriendRequests(
    userId: string
  ): Promise<(Friendship & { requester: User })[]> {
    const requests = await db
      .select({
        id: friendships.id,
        requesterId: friendships.requesterId,
        receiverId: friendships.receiverId,
        status: friendships.status,
        createdAt: friendships.createdAt,
        updatedAt: friendships.updatedAt,
        requester: {
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        },
      })
      .from(friendships)
      .innerJoin(users, eq(friendships.requesterId, users.id))
      .where(
        and(
          eq(friendships.receiverId, userId),
          eq(friendships.status, "pending")
        )
      );

    return requests;
  }

  async unfriend(userId: string, friendId: string): Promise<void> {
    await db
      .delete(friendships)
      .where(
        or(
          and(
            eq(friendships.requesterId, userId),
            eq(friendships.receiverId, friendId)
          ),
          and(
            eq(friendships.requesterId, friendId),
            eq(friendships.receiverId, userId)
          )
        )
      );
  }

  // Friend invite operations
  async sendFriendInvite(
    fromUserId: string,
    toEmail: string
  ): Promise<FriendInvite> {
    const inviteToken =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

    const [invite] = await db
      .insert(friendInvites)
      .values({
        fromUserId,
        toEmail,
        inviteToken,
        expiresAt,
      })
      .returning();
    return invite;
  }

  async acceptFriendInvite(
    inviteToken: string,
    userId: string
  ): Promise<Friendship> {
    const [invite] = await db
      .select()
      .from(friendInvites)
      .where(eq(friendInvites.inviteToken, inviteToken))
      .limit(1);

    if (
      !invite ||
      invite.status !== "pending" ||
      invite.expiresAt < new Date()
    ) {
      throw new Error("Invalid or expired invite");
    }

    // Create friendship
    const friendship = await this.sendFriendRequest(invite.fromUserId, userId);
    await this.acceptFriendRequest(friendship.id);

    // Mark invite as accepted
    await db
      .update(friendInvites)
      .set({ status: "accepted" })
      .where(eq(friendInvites.inviteToken, inviteToken));

    return friendship;
  }

  async getFriendInvites(userId: string): Promise<FriendInvite[]> {
    return await db
      .select()
      .from(friendInvites)
      .where(eq(friendInvites.fromUserId, userId))
      .orderBy(desc(friendInvites.createdAt));
  }

  async cancelFriendInvite(inviteId: number, userId: string): Promise<boolean> {
    try {
      // First check if the invite exists and belongs to the user
      const [invite] = await db
        .select()
        .from(friendInvites)
        .where(
          and(
            eq(friendInvites.id, inviteId),
            eq(friendInvites.fromUserId, userId),
            eq(friendInvites.status, "pending")
          )
        )
        .limit(1);

      if (!invite) {
        return false; // Invite not found or not owned by user or already processed
      }

      // Update the invite status to canceled
      const result = await db
        .update(friendInvites)
        .set({
          status: "canceled",
          updatedAt: new Date(),
        })
        .where(eq(friendInvites.id, inviteId));

      return true;
    } catch (error) {
      console.error("Error canceling friend invite:", error);
      return false;
    }
  }

  // Learning path operations
  async createLearningPath(
    userId: string,
    planId: number
  ): Promise<LearningPath> {
    try {
      console.log("Creating learning path for user:", userId, "plan:", planId);

      // Verify plan exists and user has access
      const userPlans = await this.getUserLearningPlans(userId);
      const plan = userPlans.find((p) => p.id === planId);
      if (!plan) {
        throw new Error(
          `Learning plan with ID ${planId} not found or access denied`
        );
      }

      // Get plan videos first to check if plan has videos
      const planVideos = await this.getPlanVideos(planId);
      console.log("Found plan videos:", planVideos.length);

      if (planVideos.length === 0) {
        throw new Error(`Plan ${planId} has no videos to create learning path`);
      }

      const [path] = await db
        .insert(learningPaths)
        .values({
          userId,
          planId,
          pathData: { nodes: [], connections: [] },
          completionPercentage: 0,
          notes: "",
        })
        .returning();

      console.log("Created learning path:", path.id);

      // Create path nodes for each video in the plan
      const createdNodes = [];
      for (let i = 0; i < planVideos.length; i++) {
        const planVideo = planVideos[i];
        const [node] = await db
          .insert(pathNodes)
          .values({
            pathId: path.id,
            videoId: planVideo.videoId,
            position: { x: 100 + i * 150, y: 100 + (i % 3) * 100 },
            isCompleted: false,
            order: planVideo.order || i,
          })
          .returning();
        createdNodes.push(node);
      }

      console.log("Created path nodes:", createdNodes.length);
      return path;
    } catch (error) {
      console.error("Error in createLearningPath:", error);
      throw error;
    }
  }

  async getLearningPath(
    userId: string,
    planId: number
  ): Promise<LearningPath | undefined> {
    const [path] = await db
      .select()
      .from(learningPaths)
      .where(
        and(eq(learningPaths.userId, userId), eq(learningPaths.planId, planId))
      )
      .limit(1);
    return path;
  }

  async updateLearningPath(
    pathId: number,
    pathData: any,
    completionPercentage: number
  ): Promise<LearningPath> {
    const [path] = await db
      .update(learningPaths)
      .set({ pathData, completionPercentage, updatedAt: new Date() })
      .where(eq(learningPaths.id, pathId))
      .returning();
    return path;
  }

  async updateLearningPathNotes(
    pathId: number,
    notes: string
  ): Promise<LearningPath> {
    const [path] = await db
      .update(learningPaths)
      .set({ notes, updatedAt: new Date() })
      .where(eq(learningPaths.id, pathId))
      .returning();
    return path;
  }

  async getPathNodes(pathId: number): Promise<(PathNode & { video: Video })[]> {
    const nodes = await db
      .select({
        id: pathNodes.id,
        pathId: pathNodes.pathId,
        videoId: pathNodes.videoId,
        position: pathNodes.position,
        isCompleted: pathNodes.isCompleted,
        completedAt: pathNodes.completedAt,
        order: pathNodes.order,
        video: {
          id: videos.id,
          youtubeId: videos.youtubeId,
          title: videos.title,
          description: videos.description,
          thumbnailUrl: videos.thumbnailUrl,
          duration: videos.duration,
          channelTitle: videos.channelTitle,
          viewCount: videos.viewCount,
          publishedAt: videos.publishedAt,
          createdAt: videos.createdAt,
        },
      })
      .from(pathNodes)
      .innerJoin(videos, eq(pathNodes.videoId, videos.id))
      .where(eq(pathNodes.pathId, pathId))
      .orderBy(asc(pathNodes.order));

    return nodes;
  }

  async updatePathNode(
    nodeId: number,
    isCompleted: boolean
  ): Promise<PathNode> {
    const [node] = await db
      .update(pathNodes)
      .set({
        isCompleted,
        completedAt: isCompleted ? new Date() : null,
      })
      .where(eq(pathNodes.id, nodeId))
      .returning();
    return node;
  }

  // Study group operations
  async createStudyGroup(groupData: InsertStudyGroup): Promise<StudyGroup> {
    const [group] = await db.insert(studyGroups).values(groupData).returning();
    return group;
  }

  async getStudyGroup(groupId: number): Promise<StudyGroup | undefined> {
    const [group] = await db
      .select()
      .from(studyGroups)
      .where(eq(studyGroups.id, groupId))
      .limit(1);
    return group;
  }

  async getUserStudyGroups(userId: string): Promise<StudyGroup[]> {
    const groups = await db
      .select({
        id: studyGroups.id,
        name: studyGroups.name,
        description: studyGroups.description,
        createdById: studyGroups.createdById,
        planId: studyGroups.planId,
        isPrivate: studyGroups.isPrivate,
        createdAt: studyGroups.createdAt,
        updatedAt: studyGroups.updatedAt,
      })
      .from(studyGroups)
      .innerJoin(
        studyGroupMembers,
        eq(studyGroups.id, studyGroupMembers.groupId)
      )
      .where(eq(studyGroupMembers.userId, userId));

    return groups;
  }

  async joinStudyGroup(
    groupId: number,
    userId: string
  ): Promise<StudyGroupMember> {
    const [member] = await db
      .insert(studyGroupMembers)
      .values({
        groupId,
        userId,
        role: "member",
      })
      .returning();
    return member;
  }

  async leaveStudyGroup(groupId: number, userId: string): Promise<void> {
    await db
      .delete(studyGroupMembers)
      .where(
        and(
          eq(studyGroupMembers.groupId, groupId),
          eq(studyGroupMembers.userId, userId)
        )
      );
  }

  async getStudyGroupMembers(
    groupId: number
  ): Promise<(StudyGroupMember & { user: User })[]> {
    const members = await db
      .select({
        id: studyGroupMembers.id,
        groupId: studyGroupMembers.groupId,
        userId: studyGroupMembers.userId,
        role: studyGroupMembers.role,
        joinedAt: studyGroupMembers.joinedAt,
        user: {
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        },
      })
      .from(studyGroupMembers)
      .innerJoin(users, eq(studyGroupMembers.userId, users.id))
      .where(eq(studyGroupMembers.groupId, groupId));

    return members;
  }

  async createGroupActivity(
    activity: InsertGroupActivity
  ): Promise<GroupActivity> {
    const [newActivity] = await db
      .insert(groupActivities)
      .values(activity)
      .returning();
    return newActivity;
  }

  async getGroupActivities(
    groupId: number
  ): Promise<(GroupActivity & { user: User })[]> {
    const activities = await db
      .select({
        id: groupActivities.id,
        groupId: groupActivities.groupId,
        userId: groupActivities.userId,
        type: groupActivities.type,
        content: groupActivities.content,
        createdAt: groupActivities.createdAt,
        user: {
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          profileImageUrl: users.profileImageUrl,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        },
      })
      .from(groupActivities)
      .innerJoin(users, eq(groupActivities.userId, users.id))
      .where(eq(groupActivities.groupId, groupId))
      .orderBy(desc(groupActivities.createdAt));

    return activities;
  }

  // AI Difficulty Optimizer operations
  async analyzeVideoDifficulty(
    videoId: number
  ): Promise<VideoDifficultyAnalysis> {
    // Check if we already have analysis for this video
    const existingAnalysis = await db
      .select()
      .from(videoDifficultyAnalysis)
      .where(eq(videoDifficultyAnalysis.videoId, videoId))
      .limit(1);

    if (existingAnalysis.length > 0) {
      return existingAnalysis[0];
    }

    // Get video details
    const video = await this.getVideo(videoId);
    if (!video) {
      throw new Error("Video not found");
    }

    // Simple difficulty analysis based on video metadata
    const analysis = this.performDifficultyAnalysis(video);

    const [newAnalysis] = await db
      .insert(videoDifficultyAnalysis)
      .values({
        videoId,
        subject: analysis.subject,
        difficultyLevel: analysis.difficultyLevel,
        prerequisites: analysis.prerequisites,
        learningObjectives: analysis.learningObjectives,
        complexityFactors: analysis.complexityFactors,
        estimatedCompletionTime: analysis.estimatedCompletionTime,
        cognitiveLoad: analysis.cognitiveLoad,
        confidence: analysis.confidence,
      })
      .returning();

    return newAnalysis;
  }

  private performDifficultyAnalysis(video: Video): any {
    const title = video.title.toLowerCase();
    const description = video.description?.toLowerCase() || "";

    // Analyze subject area
    let subject = "general";
    const subjects = {
      programming: [
        "code",
        "programming",
        "javascript",
        "python",
        "react",
        "html",
        "css",
        "api",
      ],
      mathematics: ["math", "calculus", "algebra", "statistics", "geometry"],
      design: ["design", "ui", "ux", "figma", "photoshop", "graphic"],
      business: ["business", "marketing", "finance", "startup", "entrepreneur"],
      science: ["science", "physics", "chemistry", "biology", "research"],
    };

    for (const [subj, keywords] of Object.entries(subjects)) {
      if (
        keywords.some(
          (keyword) => title.includes(keyword) || description.includes(keyword)
        )
      ) {
        subject = subj;
        break;
      }
    }

    // Determine difficulty level based on keywords
    let difficultyLevel = 5; // Default medium
    const beginnerKeywords = [
      "intro",
      "beginner",
      "basics",
      "tutorial",
      "getting started",
      "fundamentals",
    ];
    const advancedKeywords = [
      "advanced",
      "expert",
      "professional",
      "complex",
      "deep dive",
      "masterclass",
    ];

    if (
      beginnerKeywords.some(
        (kw) => title.includes(kw) || description.includes(kw)
      )
    ) {
      difficultyLevel = Math.max(1, difficultyLevel - 3);
    } else if (
      advancedKeywords.some(
        (kw) => title.includes(kw) || description.includes(kw)
      )
    ) {
      difficultyLevel = Math.min(10, difficultyLevel + 3);
    }

    // Estimate completion time based on duration
    const duration = video.duration || "";
    let estimatedTime = 30; // default 30 minutes
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (match) {
      const hours = parseInt(match[1]) || 0;
      const minutes = parseInt(match[2]) || 0;
      estimatedTime = hours * 60 + minutes;
    }

    return {
      subject,
      difficultyLevel,
      prerequisites: [],
      learningObjectives: [subject, "skill development"],
      complexityFactors: {
        pace: "medium",
        terminology: difficultyLevel > 6 ? "advanced" : "basic",
        concepts: difficultyLevel > 7 ? "complex" : "simple",
      },
      estimatedCompletionTime: estimatedTime,
      cognitiveLoad: Math.min(
        10,
        Math.max(1, Math.round(difficultyLevel * 0.8))
      ),
      confidence: 0.7,
    };
  }

  async getUserSkillProfile(
    userId: string,
    subject?: string
  ): Promise<UserSkillProfile[]> {
    const query = db
      .select()
      .from(userSkillProfiles)
      .where(eq(userSkillProfiles.userId, userId));

    if (subject) {
      query.where(
        and(
          eq(userSkillProfiles.userId, userId),
          eq(userSkillProfiles.subject, subject)
        )
      );
    }

    return await query.orderBy(desc(userSkillProfiles.lastAssessed));
  }

  async updateUserSkillProfile(
    userId: string,
    subject: string,
    data: Partial<InsertUserSkillProfile>
  ): Promise<UserSkillProfile> {
    const existing = await db
      .select()
      .from(userSkillProfiles)
      .where(
        and(
          eq(userSkillProfiles.userId, userId),
          eq(userSkillProfiles.subject, subject)
        )
      )
      .limit(1);

    if (existing.length > 0) {
      const [updated] = await db
        .update(userSkillProfiles)
        .set({
          ...data,
          updatedAt: new Date(),
          lastAssessed: new Date(),
        })
        .where(eq(userSkillProfiles.id, existing[0].id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(userSkillProfiles)
        .values({
          userId,
          subject,
          ...data,
        })
        .returning();
      return created;
    }
  }

  async getOptimizedVideoRecommendations(
    userId: string,
    planId: number,
    limit: number = 10
  ): Promise<any[]> {
    // Get user's skill profiles
    const skillProfiles = await this.getUserSkillProfile(userId);

    // Get plan videos with difficulty analysis
    const planVideos = await db
      .select({
        video: videos,
        planVideo: planVideos,
        difficulty: videoDifficultyAnalysis,
      })
      .from(planVideos)
      .innerJoin(videos, eq(planVideos.videoId, videos.id))
      .leftJoin(
        videoDifficultyAnalysis,
        eq(videos.id, videoDifficultyAnalysis.videoId)
      )
      .where(eq(planVideos.planId, planId))
      .orderBy(planVideos.orderIndex);

    // Get user's progress
    const userProgress = await this.getUserProgressForPlan(userId, planId);
    const completedVideoIds = new Set(
      userProgress.filter((p) => p.isCompleted).map((p) => p.videoId)
    );

    // Filter out completed videos and sort by optimal difficulty progression
    const incompleteVideos = planVideos.filter(
      (pv) => !completedVideoIds.has(pv.video.id)
    );

    // Calculate optimal ordering based on difficulty progression
    const optimizedVideos = this.optimizeDifficultyProgression(
      incompleteVideos,
      skillProfiles,
      userProgress
    );

    return optimizedVideos.slice(0, limit).map((item) => ({
      ...item.video,
      difficulty: item.difficulty,
      recommendedOrder: item.recommendedOrder,
      adaptationReason: item.adaptationReason,
    }));
  }

  private optimizeDifficultyProgression(
    videos: any[],
    skillProfiles: UserSkillProfile[],
    userProgress: any[]
  ): any[] {
    // Calculate user's average skill level
    const avgSkillLevel =
      skillProfiles.length > 0
        ? skillProfiles.reduce((sum, profile) => sum + profile.skillLevel, 0) /
          skillProfiles.length
        : 3; // Default beginner-intermediate

    // Sort videos by optimal difficulty progression
    return videos
      .map((item, index) => {
        const difficulty = item.difficulty?.difficultyLevel || 5;
        const optimalDifficulty = Math.min(10, avgSkillLevel + 2); // Slightly challenging

        const difficultyGap = Math.abs(difficulty - optimalDifficulty);
        const progressionScore = this.calculateProgressionScore(
          difficulty,
          avgSkillLevel,
          index
        );

        return {
          ...item,
          recommendedOrder: index,
          progressionScore,
          difficultyGap,
          adaptationReason: this.generateAdaptationReason(
            difficulty,
            avgSkillLevel
          ),
        };
      })
      .sort((a, b) => {
        // Prioritize lower difficulty gap and higher progression score
        const scoreDiff = b.progressionScore - a.progressionScore;
        if (Math.abs(scoreDiff) > 0.1) return scoreDiff;
        return a.difficultyGap - b.difficultyGap;
      });
  }

  private calculateProgressionScore(
    videoDifficulty: number,
    userSkill: number,
    position: number
  ): number {
    const idealProgression = Math.min(10, userSkill + position * 0.5); // Gradual increase
    const progressionFit =
      1 - Math.abs(videoDifficulty - idealProgression) / 10;
    return Math.max(0, progressionFit);
  }

  private generateAdaptationReason(
    videoDifficulty: number,
    userSkill: number
  ): string {
    const gap = videoDifficulty - userSkill;

    if (gap > 3)
      return "This video might be challenging - consider reviewing prerequisites first";
    if (gap > 1) return "Good challenge level - will help you grow your skills";
    if (gap < -2)
      return "This might be too easy - consider skipping if you're confident";
    return "Perfect difficulty match for your current skill level";
  }

  async recordLearningAdaptation(
    adaptation: InsertLearningAdaptation
  ): Promise<LearningAdaptation> {
    const [newAdaptation] = await db
      .insert(learningAdaptations)
      .values(adaptation)
      .returning();

    // Update user skill profile based on the adaptation
    if (adaptation.userFeedback) {
      await this.updateSkillBasedOnFeedback(
        adaptation.userId,
        adaptation.videoId,
        adaptation.userFeedback
      );
    }

    return newAdaptation;
  }

  private async updateSkillBasedOnFeedback(
    userId: string,
    videoId: number,
    feedback: number
  ): Promise<void> {
    // Get video difficulty analysis
    const difficulty = await db
      .select()
      .from(videoDifficultyAnalysis)
      .where(eq(videoDifficultyAnalysis.videoId, videoId))
      .limit(1);

    if (difficulty.length === 0) return;

    const subject = difficulty[0].subject;
    const videoDifficulty = difficulty[0].difficultyLevel;

    // Get or create user skill profile
    const skillProfiles = await this.getUserSkillProfile(userId, subject);
    let currentSkill =
      skillProfiles.length > 0 ? skillProfiles[0].skillLevel : 3;

    // Adjust skill based on feedback
    let skillAdjustment = 0;
    if (feedback <= 2) {
      // Too easy
      skillAdjustment = 0.5;
    } else if (feedback >= 4) {
      // Too hard
      skillAdjustment = -0.3;
    } else {
      // Just right
      skillAdjustment = 0.2;
    }

    const newSkillLevel = Math.max(
      1,
      Math.min(10, currentSkill + skillAdjustment)
    );

    await this.updateUserSkillProfile(userId, subject, {
      skillLevel: Math.round(newSkillLevel * 10) / 10, // Round to 1 decimal
      confidence: Math.min(1, (skillProfiles[0]?.confidence || 0.5) + 0.1),
    });
  }

  async getDifficultyProgressionPath(
    userId: string,
    subject: string
  ): Promise<any[]> {
    const skillProfile = await this.getUserSkillProfile(userId, subject);
    const currentSkill =
      skillProfile.length > 0 ? skillProfile[0].skillLevel : 3;

    // Get all videos for this subject with difficulty analysis
    const videosWithDifficulty = await db
      .select({
        video: videos,
        difficulty: videoDifficultyAnalysis,
      })
      .from(videos)
      .innerJoin(
        videoDifficultyAnalysis,
        eq(videos.id, videoDifficultyAnalysis.videoId)
      )
      .where(eq(videoDifficultyAnalysis.subject, subject))
      .orderBy(videoDifficultyAnalysis.difficultyLevel);

    // Create optimal learning path
    return this.createOptimalLearningPath(videosWithDifficulty, currentSkill);
  }

  private createOptimalLearningPath(
    videos: any[],
    currentSkill: number
  ): any[] {
    const path = [];
    let targetDifficulty = currentSkill;

    // Group videos by difficulty level
    const videosByDifficulty = videos.reduce((acc, item) => {
      const level = item.difficulty.difficultyLevel;
      if (!acc[level]) acc[level] = [];
      acc[level].push(item);
      return acc;
    }, {} as Record<number, any[]>);

    // Build progression path
    for (
      let level = Math.max(1, Math.floor(currentSkill - 1));
      level <= 10;
      level++
    ) {
      const levelVideos = videosByDifficulty[level] || [];
      if (levelVideos.length > 0) {
        // Select best video for this level
        const bestVideo = levelVideos[0]; // Could add more sophisticated selection
        path.push({
          ...bestVideo,
          pathOrder: path.length + 1,
          isCurrentLevel: level === Math.round(currentSkill),
          isUnlocked: level <= currentSkill + 2, // Allow slight challenge
        });
      }
    }

    return path;
  }

  async analyzeUserStrugglePatterns(userId: string): Promise<any> {
    // Get user's learning adaptations and video progress
    const adaptations = await db
      .select()
      .from(learningAdaptations)
      .where(eq(learningAdaptations.userId, userId))
      .orderBy(desc(learningAdaptations.createdAt))
      .limit(50);

    const progress = await db
      .select()
      .from(videoProgress)
      .where(eq(videoProgress.userId, userId))
      .orderBy(desc(videoProgress.lastWatched))
      .limit(100);

    // Analyze patterns
    const strugglingSubjects = this.identifyStrugglingSubjects(
      adaptations,
      progress
    );
    const commonIssues = this.identifyCommonIssues(adaptations);
    const learningVelocity = this.calculateLearningVelocity(progress);

    return {
      strugglingSubjects,
      commonIssues,
      learningVelocity,
      recommendations: this.generateStruggleRecommendations(
        strugglingSubjects,
        commonIssues
      ),
    };
  }

  private identifyStrugglingSubjects(
    adaptations: LearningAdaptation[],
    progress: any[]
  ): string[] {
    // Find subjects with low completion rates or high difficulty feedback
    const subjectStats = {} as Record<
      string,
      { total: number; struggles: number }
    >;

    // This would need video difficulty data - simplified for now
    return ["programming", "mathematics"].filter(() => Math.random() > 0.7); // Mock implementation
  }

  private identifyCommonIssues(adaptations: LearningAdaptation[]): string[] {
    const issues = [];

    const skippedCount = adaptations.filter((a) => a.wasSkipped).length;
    if (skippedCount > adaptations.length * 0.3) {
      issues.push("Frequently skips challenging content");
    }

    const lowFeedback = adaptations.filter(
      (a) => (a.userFeedback || 0) >= 4
    ).length;
    if (lowFeedback > adaptations.length * 0.4) {
      issues.push("Content often too difficult");
    }

    return issues;
  }

  private calculateLearningVelocity(progress: any[]): number {
    if (progress.length < 2) return 1;

    const completedLast7Days = progress.filter((p) => {
      const daysDiff =
        (new Date().getTime() - new Date(p.lastWatched).getTime()) /
        (1000 * 60 * 60 * 24);
      return daysDiff <= 7 && p.isCompleted;
    }).length;

    return Math.max(0.1, completedLast7Days / 7); // Videos per day
  }

  private generateStruggleRecommendations(
    subjects: string[],
    issues: string[]
  ): string[] {
    const recommendations = [];

    if (subjects.length > 0) {
      recommendations.push(`Focus on fundamentals in ${subjects.join(", ")}`);
    }

    if (issues.includes("Frequently skips challenging content")) {
      recommendations.push(
        "Try breaking down complex topics into smaller segments"
      );
    }

    if (issues.includes("Content often too difficult")) {
      recommendations.push("Consider reviewing prerequisite materials first");
    }

    return recommendations;
  }

  async getAdaptiveLearningInsights(userId: string): Promise<any> {
    const [skillProfiles, adaptations, patterns] = await Promise.all([
      this.getUserSkillProfile(userId),
      db
        .select()
        .from(learningAdaptations)
        .where(eq(learningAdaptations.userId, userId))
        .limit(20),
      this.analyzeUserStrugglePatterns(userId),
    ]);

    const overallProgress = this.calculateOverallProgress(
      skillProfiles,
      adaptations
    );
    const learningStyle = this.determineLearningStyle(adaptations);
    const nextSteps = this.generateNextSteps(skillProfiles, patterns);

    return {
      overallProgress,
      learningStyle,
      strugglingAreas: patterns.strugglingSubjects,
      strengths: this.identifyStrengths(skillProfiles),
      nextSteps,
      adaptationTrends: this.analyzeAdaptationTrends(adaptations),
    };
  }

  private calculateOverallProgress(
    profiles: UserSkillProfile[],
    adaptations: LearningAdaptation[]
  ): any {
    const avgSkill =
      profiles.length > 0
        ? profiles.reduce((sum, p) => sum + p.skillLevel, 0) / profiles.length
        : 3;

    const completionRate =
      profiles.length > 0
        ? profiles.reduce((sum, p) => sum + p.completionRate, 0) /
          profiles.length
        : 0;

    return {
      averageSkillLevel: Math.round(avgSkill * 10) / 10,
      overallCompletionRate: Math.round(completionRate * 100),
      totalSubjects: profiles.length,
      recentAdaptations: adaptations.length,
    };
  }

  private determineLearningStyle(adaptations: LearningAdaptation[]): string {
    // Analyze feedback patterns to determine learning style
    const avgFeedback =
      adaptations.length > 0
        ? adaptations
            .filter((a) => a.userFeedback)
            .reduce((sum, a) => sum + (a.userFeedback || 0), 0) /
          adaptations.filter((a) => a.userFeedback).length
        : 3;

    if (avgFeedback < 2.5) return "Prefers challenging content";
    if (avgFeedback > 3.5) return "Prefers gradual progression";
    return "Balanced learning approach";
  }

  private identifyStrengths(profiles: UserSkillProfile[]): string[] {
    return profiles
      .filter((p) => p.skillLevel >= 7)
      .map((p) => p.subject)
      .slice(0, 3);
  }

  private generateNextSteps(
    profiles: UserSkillProfile[],
    patterns: any
  ): string[] {
    const steps = [];

    if (profiles.length === 0) {
      steps.push("Complete a few videos to establish your skill profile");
    } else {
      const lowestSkill = profiles.reduce((min, p) =>
        p.skillLevel < min.skillLevel ? p : min
      );
      steps.push(`Focus on improving ${lowestSkill.subject} skills`);
    }

    if (patterns.learningVelocity < 0.5) {
      steps.push("Try to maintain a more consistent learning schedule");
    }

    steps.push(
      "Provide feedback on video difficulty to improve recommendations"
    );

    return steps;
  }

  private analyzeAdaptationTrends(adaptations: LearningAdaptation[]): any {
    const recent = adaptations.slice(0, 10);
    const older = adaptations.slice(10, 20);

    const recentAvgFeedback =
      recent.length > 0
        ? recent
            .filter((a) => a.userFeedback)
            .reduce((sum, a) => sum + (a.userFeedback || 0), 0) /
          recent.filter((a) => a.userFeedback).length
        : 3;

    const olderAvgFeedback =
      older.length > 0
        ? older
            .filter((a) => a.userFeedback)
            .reduce((sum, a) => sum + (a.userFeedback || 0), 0) /
          older.filter((a) => a.userFeedback).length
        : 3;

    const trend =
      recentAvgFeedback > olderAvgFeedback
        ? "improving"
        : recentAvgFeedback < olderAvgFeedback
        ? "struggling"
        : "stable";

    return {
      trend,
      recentDifficulty: recentAvgFeedback,
      improvementRate: recentAvgFeedback - olderAvgFeedback,
    };
  }

  // Common Playlist operations
  async getCommonPlaylist(category?: string): Promise<CommonPlaylistItem[]> {
    const query = db
      .select()
      .from(commonPlaylist)
      .where(eq(commonPlaylist.isActive, true));

    if (category) {
      return await query.where(eq(commonPlaylist.category, category));
    }

    return await query.orderBy(commonPlaylist.category, commonPlaylist.title);
  }

  async addToCommonPlaylist(
    item: InsertCommonPlaylistItem
  ): Promise<CommonPlaylistItem> {
    const [newItem] = await db.insert(commonPlaylist).values(item).returning();
    return newItem;
  }

  async getUserPlaylistSelections(
    userId: string
  ): Promise<UserPlaylistSelection[]> {
    return await db
      .select()
      .from(userPlaylistSelections)
      .where(eq(userPlaylistSelections.userId, userId))
      .orderBy(userPlaylistSelections.createdAt);
  }

  async addUserPlaylistSelection(
    selection: InsertUserPlaylistSelection
  ): Promise<UserPlaylistSelection> {
    const [newSelection] = await db
      .insert(userPlaylistSelections)
      .values(selection)
      .returning();
    return newSelection;
  }

  async removeUserPlaylistSelection(
    userId: string,
    playlistItemId: number
  ): Promise<void> {
    await db
      .delete(userPlaylistSelections)
      .where(
        and(
          eq(userPlaylistSelections.userId, userId),
          eq(userPlaylistSelections.playlistItemId, playlistItemId)
        )
      );
  }

  async createPlanFromPlaylistItems(
    userId: string,
    title: string,
    playlistItemIds: number[]
  ): Promise<LearningPlan> {
    // Create the learning plan
    const [plan] = await db
      .insert(learningPlans)
      .values({
        userId,
        title,
        description: `Learning plan created from common playlist items`,
        isActive: true,
      })
      .returning();

    // Get the playlist items
    const playlistItems = await db
      .select()
      .from(commonPlaylist)
      .where(inArray(commonPlaylist.id, playlistItemIds));

    // For each playlist item, search for videos and add them to the plan
    for (const item of playlistItems) {
      // This would typically involve searching YouTube API with the search terms
      // For now, we'll create placeholder entries that can be filled later
      console.log(`Adding playlist item "${item.title}" to plan ${plan.id}`);
    }

    return plan;
  }

  async addPlaylistItemToPlan(
    userId: string,
    playlistItemId: number,
    planId: number
  ): Promise<void> {
    // Get the playlist item
    const [playlistItem] = await db
      .select()
      .from(commonPlaylist)
      .where(eq(commonPlaylist.id, playlistItemId));

    if (!playlistItem) {
      throw new Error(`Playlist item ${playlistItemId} not found`);
    }

    // Verify user owns the plan
    const userPlans = await this.getUserLearningPlans(userId);
    const plan = userPlans.find((p) => p.id === planId);
    if (!plan) {
      throw new Error(`Plan ${planId} not found or access denied`);
    }

    // Record the selection
    await this.addUserPlaylistSelection({
      userId,
      playlistItemId,
      addedToPlanId: planId,
      isBookmarked: false,
    });

    console.log(
      `Added playlist item "${playlistItem.title}" to plan ${planId}`
    );
  }
}

export const storage = new DatabaseStorage();
