@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Brand colors from design */
  --brand-blue: hsl(219, 84%, 59%);
  --brand-violet: hsl(258, 70%, 66%);
  --brand-emerald: hsl(160, 84%, 39%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

.modern-blue {
  --background: hsl(215, 100%, 98%);
  --foreground: hsl(215, 25%, 27%);
  --muted: hsl(215, 100%, 96%);
  --muted-foreground: hsl(215, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(215, 25%, 27%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(215, 25%, 27%);
  --border: hsl(215, 32%, 91%);
  --input: hsl(215, 32%, 91%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(215, 100%, 94%);
  --secondary-foreground: hsl(215, 25%, 27%);
  --accent: hsl(215, 100%, 94%);
  --accent-foreground: hsl(215, 25%, 27%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(217, 91%, 60%);
  --radius: 0.75rem;
}

.modern-purple {
  --background: hsl(270, 100%, 98%);
  --foreground: hsl(270, 15%, 30%);
  --muted: hsl(270, 100%, 96%);
  --muted-foreground: hsl(270, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(270, 15%, 30%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(270, 15%, 30%);
  --border: hsl(270, 32%, 91%);
  --input: hsl(270, 32%, 91%);
  --primary: hsl(272, 81%, 67%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(270, 100%, 94%);
  --secondary-foreground: hsl(270, 15%, 30%);
  --accent: hsl(270, 100%, 94%);
  --accent-foreground: hsl(270, 15%, 30%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(272, 81%, 67%);
  --radius: 0.75rem;
}

.modern-green {
  --background: hsl(142, 100%, 98%);
  --foreground: hsl(142, 25%, 25%);
  --muted: hsl(142, 100%, 96%);
  --muted-foreground: hsl(142, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(142, 25%, 25%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(142, 25%, 25%);
  --border: hsl(142, 32%, 91%);
  --input: hsl(142, 32%, 91%);
  --primary: hsl(142, 76%, 47%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(142, 100%, 94%);
  --secondary-foreground: hsl(142, 25%, 25%);
  --accent: hsl(142, 100%, 94%);
  --accent-foreground: hsl(142, 25%, 25%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(142, 76%, 47%);
  --radius: 0.75rem;
}

.modern-orange {
  --background: hsl(24, 100%, 98%);
  --foreground: hsl(24, 25%, 25%);
  --muted: hsl(24, 100%, 96%);
  --muted-foreground: hsl(24, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(24, 25%, 25%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(24, 25%, 25%);
  --border: hsl(24, 32%, 91%);
  --input: hsl(24, 32%, 91%);
  --primary: hsl(24, 84%, 58%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(24, 100%, 94%);
  --secondary-foreground: hsl(24, 25%, 25%);
  --accent: hsl(24, 100%, 94%);
  --accent-foreground: hsl(24, 25%, 25%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(24, 84%, 58%);
  --radius: 0.75rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-slate-50 text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .brand-blue {
    @apply text-blue-500;
  }
  
  .brand-violet {
    @apply text-violet-500;
  }
  
  .brand-emerald {
    @apply text-emerald-500;
  }
  
  .bg-brand-blue {
    background-color: var(--brand-blue);
  }
  
  .bg-brand-violet {
    background-color: var(--brand-violet);
  }
  
  .bg-brand-emerald {
    background-color: var(--brand-emerald);
  }
  
  .text-brand-blue {
    color: var(--brand-blue);
  }
  
  .text-brand-violet {
    color: var(--brand-violet);
  }
  
  .text-brand-emerald {
    color: var(--brand-emerald);
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
