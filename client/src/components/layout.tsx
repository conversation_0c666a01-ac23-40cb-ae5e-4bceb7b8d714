import NavigationHeader from "@/components/navigation-header";
import MobileNavigation from "@/components/mobile-navigation";
import Footer from "@/components/footer";

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-slate-50 flex flex-col">
      <NavigationHeader />
      <main className="flex-1">{children}</main>
      <Footer />
      <MobileNavigation />
    </div>
  );
}
