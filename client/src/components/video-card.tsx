import { Link } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Play, Clock, User } from "lucide-react";

interface VideoCardProps {
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
  progress?: {
    currentTime: number;
    isCompleted: boolean;
    progressPercentage: number;
  };
  category?: string;
  showProgress?: boolean;
  className?: string;
}

export default function VideoCard({ 
  video, 
  progress, 
  category, 
  showProgress = true,
  className = "" 
}: VideoCardProps) {
  const categoryColors = {
    'Web Development': 'bg-blue-100 text-blue-500',
    'Data Science': 'bg-violet-100 text-violet-500',
    'Programming': 'bg-emerald-100 text-emerald-500',
    default: 'bg-slate-100 text-slate-500',
  };

  const categoryClass = categoryColors[category as keyof typeof categoryColors] || categoryColors.default;

  return (
    <Card className={`border border-slate-200 overflow-hidden hover:shadow-md transition-shadow ${className}`}>
      <Link href={`/video/${video.id}`}>
        <div className="relative">
          <img 
            src={video.thumbnailUrl || '/api/placeholder/600/300'} 
            alt={video.title}
            className="w-full h-32 object-cover cursor-pointer hover:opacity-90 transition-opacity"
          />
          {progress?.isCompleted && (
            <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
              <Play className="w-3 h-3" />
            </div>
          )}
        </div>
      </Link>
      
      <CardContent className="p-4">
        {/* Category and Duration */}
        <div className="flex items-center justify-between mb-2">
          {category && (
            <span className={`text-xs px-2 py-1 rounded-full font-medium ${categoryClass}`}>
              {category}
            </span>
          )}
          <div className="flex items-center text-xs text-slate-500">
            <Clock className="w-3 h-3 mr-1" />
            {video.duration}
          </div>
        </div>
        
        {/* Title */}
        <Link href={`/video/${video.id}`}>
          <h4 className="font-semibold text-slate-800 mb-2 line-clamp-2 cursor-pointer hover:text-blue-600 transition-colors">
            {video.title}
          </h4>
        </Link>
        
        {/* Channel */}
        <div className="flex items-center text-sm text-slate-600 mb-3">
          <User className="w-3 h-3 mr-1" />
          {video.channelTitle}
        </div>
        
        {/* Progress */}
        {showProgress && progress && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-slate-600">Progress</span>
              <span className="font-medium text-slate-800">
                {progress.isCompleted ? 'Completed' : `${Math.round(progress.progressPercentage)}%`}
              </span>
            </div>
            <Progress value={progress.progressPercentage} className="h-2" />
          </div>
        )}
        
        {/* Action Button */}
        <Link href={`/video/${video.id}`}>
          <Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600 text-white">
            <Play className="w-4 h-4 mr-2" />
            {progress?.isCompleted ? 'Watch Again' : progress ? 'Continue' : 'Watch'}
          </Button>
        </Link>
      </CardContent>
    </Card>
  );
}
