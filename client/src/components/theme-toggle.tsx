import { useTheme } from "./theme-provider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Palette, Check } from "lucide-react";

const themes = [
  {
    name: "Light",
    value: "light" as const,
    description: "Clean and minimal light theme",
    preview: "bg-white text-slate-900",
    color: "bg-slate-100"
  },
  {
    name: "Dark",
    value: "dark" as const,
    description: "Easy on the eyes dark theme",
    preview: "bg-slate-900 text-white",
    color: "bg-slate-800"
  },
  {
    name: "Modern Blue",
    value: "modern-blue" as const,
    description: "Professional blue accent theme",
    preview: "bg-blue-50 text-blue-900",
    color: "bg-blue-500"
  },
  {
    name: "Modern Purple",
    value: "modern-purple" as const,
    description: "Creative purple accent theme",
    preview: "bg-purple-50 text-purple-900",
    color: "bg-purple-500"
  },
  {
    name: "Modern Green",
    value: "modern-green" as const,
    description: "Fresh green accent theme",
    preview: "bg-green-50 text-green-900",
    color: "bg-green-500"
  },
  {
    name: "Modern Orange",
    value: "modern-orange" as const,
    description: "Energetic orange accent theme",
    preview: "bg-orange-50 text-orange-900",
    color: "bg-orange-500"
  }
];

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Palette className="h-4 w-4" />
          <span className="hidden sm:inline">Theme</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Choose Your Theme</DialogTitle>
          <DialogDescription>
            Select from our collection of beautiful themes to personalize your learning experience.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-3 py-4">
          {themes.map((themeOption) => (
            <div
              key={themeOption.value}
              className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all hover:border-blue-500 ${
                theme === themeOption.value 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-slate-200 hover:bg-slate-50'
              }`}
              onClick={() => setTheme(themeOption.value)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`h-6 w-6 rounded-full ${themeOption.color}`} />
                  <div>
                    <div className="font-medium">{themeOption.name}</div>
                    <div className="text-sm text-slate-600">{themeOption.description}</div>
                  </div>
                </div>
                {theme === themeOption.value && (
                  <Check className="h-5 w-5 text-blue-500" />
                )}
              </div>
              <div className={`mt-3 h-8 rounded ${themeOption.preview} flex items-center justify-center text-xs font-medium`}>
                Preview
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}