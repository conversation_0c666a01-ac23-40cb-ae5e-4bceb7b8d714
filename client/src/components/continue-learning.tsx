import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Play, Clock } from "lucide-react";

interface VideoWithProgress {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  progress?: {
    currentTime: number;
    isCompleted: boolean;
    progressPercentage: number;
  };
  category?: string;
}

export default function ContinueLearning() {
  const { user } = useAuth();
  
  const { data: continueVideos = [], isLoading } = useQuery({
    queryKey: ["/api/continue-learning"],
    enabled: !!user,
  });

  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-slate-800">Continue Learning</h3>
        </div>
        <div className="grid sm:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="w-full h-32 bg-slate-200 rounded-t-xl"></div>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                  <div className="h-2 bg-slate-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (continueVideos.length === 0) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-slate-800">Continue Learning</h3>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <div className="text-slate-400 mb-4">
              <Play className="w-12 h-12 mx-auto" />
            </div>
            <h4 className="text-lg font-medium text-slate-800 mb-2">No videos in progress</h4>
            <p className="text-slate-600 mb-4">
              Start watching videos to see your progress here.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-slate-800">Continue Learning</h3>
        <Link href="/progress">
          <a className="text-blue-500 text-sm font-medium hover:text-blue-600 transition-colors">
            View All
          </a>
        </Link>
      </div>

      <div className="grid sm:grid-cols-2 gap-6">
        {continueVideos.slice(0, 2).map((video: VideoWithProgress) => (
          <Card key={video.id} className="border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
            <Link href={`/video/${video.id}`}>
              <img 
                src={video.thumbnailUrl || '/api/placeholder/600/300'} 
                alt={video.title}
                className="w-full h-32 object-cover cursor-pointer hover:opacity-90 transition-opacity"
              />
            </Link>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 mb-2">
                <span className="bg-blue-100 text-blue-500 text-xs px-2 py-1 rounded-full font-medium">
                  {video.category || 'Learning'}
                </span>
                <div className="flex items-center text-xs text-slate-500">
                  <Clock className="w-3 h-3 mr-1" />
                  {video.duration}
                </div>
              </div>
              <Link href={`/video/${video.id}`}>
                <h4 className="font-semibold text-slate-800 mb-2 line-clamp-2 cursor-pointer hover:text-blue-600 transition-colors">
                  {video.title}
                </h4>
              </Link>
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm text-slate-600">
                  Progress: {video.progress?.progressPercentage || 0}%
                </div>
                <Link href={`/video/${video.id}`}>
                  <Button variant="ghost" size="sm" className="text-blue-500 hover:text-blue-600">
                    <Play className="w-4 h-4" />
                  </Button>
                </Link>
              </div>
              <Progress 
                value={video.progress?.progressPercentage || 0} 
                className="h-2"
              />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
