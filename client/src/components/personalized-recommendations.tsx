import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Play, Plus, Clock, Eye, Sparkles } from "lucide-react";
import { Link } from "wouter";

interface RecommendedVideo {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: string;
  relevanceScore: number;
}

interface UserPatterns {
  favoriteChannels: { channelTitle: string; watchCount: number }[];
  averageWatchTime: number;
  mostActiveHours: number[];
  totalVideosCompleted: number;
}

export default function PersonalizedRecommendations() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: recommendations = [], isLoading: recommendationsLoading } = useQuery({
    queryKey: ['/api/recommendations'],
    retry: false,
  });

  const { data: patterns } = useQuery({
    queryKey: ['/api/user/patterns'],
    retry: false,
  });

  const addToLibraryMutation = useMutation({
    mutationFn: async (video: RecommendedVideo) => {
      await apiRequest("POST", "/api/videos", {
        youtubeId: video.youtubeId,
        title: video.title,
        description: video.description,
        thumbnailUrl: video.thumbnailUrl,
        duration: video.duration,
        channelTitle: video.channelTitle,
        publishedAt: video.publishedAt,
        viewCount: video.viewCount,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/videos'] });
      toast({
        title: "Success",
        description: "Video added to your library",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to add video to library",
        variant: "destructive",
      });
    },
  });

  const formatDuration = (duration: string) => {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return duration;
    
    const hours = match[1] ? parseInt(match[1]) : 0;
    const minutes = match[2] ? parseInt(match[2]) : 0;
    const seconds = match[3] ? parseInt(match[3]) : 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`;
    }
    return `${count} views`;
  };

  if (recommendationsLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          <h2 className="text-xl font-semibold">Recommended for You</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video bg-gray-300 rounded-t-lg"></div>
              <CardContent className="p-4">
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Insights */}
      {patterns && patterns.totalVideosCompleted > 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Sparkles className="w-5 h-5" />
              Your Learning Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-purple-600">{patterns.totalVideosCompleted}</div>
                <div className="text-gray-600">Videos Completed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{Math.round(patterns.averageWatchTime / 60)}m</div>
                <div className="text-gray-600">Avg. Watch Time</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{patterns.favoriteChannels.length}</div>
                <div className="text-gray-600">Favorite Channels</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {patterns.mostActiveHours.length > 0 ? `${patterns.mostActiveHours[0]}:00` : '--'}
                </div>
                <div className="text-gray-600">Peak Learning Hour</div>
              </div>
            </div>
            {patterns.favoriteChannels.length > 0 && (
              <div className="mt-4">
                <div className="text-sm font-medium text-gray-700 mb-2">Top Channels:</div>
                <div className="flex flex-wrap gap-2">
                  {patterns.favoriteChannels.slice(0, 3).map((channel, index) => (
                    <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-700">
                      {channel.channelTitle} ({channel.watchCount})
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          <h2 className="text-xl font-semibold">
            {patterns && patterns.totalVideosCompleted > 0 
              ? "Recommended for You" 
              : "Popular Videos to Get Started"
            }
          </h2>
          {patterns && patterns.totalVideosCompleted > 0 && (
            <Badge variant="outline" className="text-xs">
              Based on your viewing history
            </Badge>
          )}
        </div>

        {recommendations.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No recommendations yet</h3>
              <p className="text-gray-600 mb-4">
                Complete a few videos to get personalized recommendations based on your interests.
              </p>
              <Link href="/library">
                <Button>Browse Video Library</Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recommendations.map((video: RecommendedVideo) => (
              <Card key={video.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative aspect-video">
                  <img
                    src={video.thumbnailUrl}
                    alt={video.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                    <Link href={`/video/${video.id}`}>
                      <Button size="sm" className="bg-white text-black hover:bg-gray-100">
                        <Play className="w-4 h-4 mr-2" />
                        Watch
                      </Button>
                    </Link>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                    {formatDuration(video.duration)}
                  </div>
                  {video.relevanceScore > 2 && (
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-purple-500 text-white">
                        <Sparkles className="w-3 h-3 mr-1" />
                        For You
                      </Badge>
                    </div>
                  )}
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-sm mb-2 line-clamp-2" title={video.title}>
                    {video.title}
                  </h3>
                  <p className="text-xs text-gray-600 mb-2">{video.channelTitle}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {formatViewCount(video.viewCount)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatDuration(video.duration)}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Link href={`/video/${video.id}`} className="flex-1">
                      <Button size="sm" className="w-full">
                        <Play className="w-4 h-4 mr-2" />
                        Watch
                      </Button>
                    </Link>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => addToLibraryMutation.mutate(video)}
                      disabled={addToLibraryMutation.isPending}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}