import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { Plus, BookmarkPlus, Clock, BookOpen } from "lucide-react";
import type { CommonPlaylistItem, LearningPlan } from "@shared/schema";

export default function CommonPlaylist() {
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPlanTitle, setNewPlanTitle] = useState("");

  // Fetch common playlist
  const { data: playlist = [], isLoading: playlistLoading } = useQuery({
    queryKey: ['/api/common-playlist', selectedCategory],
    queryFn: () => apiRequest(`/api/common-playlist${selectedCategory && selectedCategory !== 'all' ? `?category=${selectedCategory}` : ''}`),
  });

  // Fetch user plans for adding items
  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
  });

  // Create new plan from selected items
  const createPlanMutation = useMutation({
    mutationFn: (data: { title: string; playlistItemIds: number[] }) =>
      apiRequest('/api/plans/from-playlist', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "New learning plan created from selected courses.",
      });
      setSelectedItems([]);
      setNewPlanTitle("");
      setIsDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create plan from selected courses.",
        variant: "destructive",
      });
    },
  });

  // Add item to existing plan
  const addToPlanMutation = useMutation({
    mutationFn: (data: { planId: number; playlistItemId: number }) =>
      apiRequest(`/api/plans/${data.planId}/add-playlist-item`, {
        method: 'POST',
        body: JSON.stringify({ playlistItemId: data.playlistItemId }),
      }),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Course added to your learning plan.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to add course to plan.",
        variant: "destructive",
      });
    },
  });

  // Bookmark item
  const bookmarkMutation = useMutation({
    mutationFn: (playlistItemId: number) =>
      apiRequest('/api/user-playlist-selections', {
        method: 'POST',
        body: JSON.stringify({ playlistItemId, isBookmarked: true }),
      }),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Course bookmarked for later.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error", 
        description: "Failed to bookmark course.",
        variant: "destructive",
      });
    },
  });

  const categories = ["creative", "career", "cognitive", "practical", "trending"];

  const groupedPlaylist = playlist.reduce((acc: Record<string, CommonPlaylistItem[]>, item: CommonPlaylistItem) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});

  const handleItemSelect = (itemId: number) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (playlistLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Common Playlist</h2>
          <p className="text-muted-foreground">Discover popular courses and skills to enhance your learning journey</p>
        </div>
        
        <div className="flex gap-2">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {selectedItems.length > 0 && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Plan ({selectedItems.length})
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Learning Plan</DialogTitle>
                  <DialogDescription>
                    Create a new learning plan with {selectedItems.length} selected courses.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="plan-title">Plan Title</Label>
                    <Input
                      id="plan-title"
                      value={newPlanTitle}
                      onChange={(e) => setNewPlanTitle(e.target.value)}
                      placeholder="Enter plan title..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    onClick={() => createPlanMutation.mutate({ title: newPlanTitle, playlistItemIds: selectedItems })}
                    disabled={!newPlanTitle.trim() || createPlanMutation.isPending}
                  >
                    {createPlanMutation.isPending ? "Creating..." : "Create Plan"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {selectedCategory && selectedCategory !== 'all' ? (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold capitalize">{selectedCategory} Skills</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(groupedPlaylist[selectedCategory] || []).map((item: CommonPlaylistItem) => (
              <PlaylistItemCard
                key={item.id}
                item={item}
                isSelected={selectedItems.includes(item.id)}
                onSelect={() => handleItemSelect(item.id)}
                onAddToPlan={(planId) => addToPlanMutation.mutate({ planId, playlistItemId: item.id })}
                onBookmark={() => bookmarkMutation.mutate(item.id)}
                userPlans={userPlans}
                getDifficultyColor={getDifficultyColor}
                isAddingToPlan={addToPlanMutation.isPending}
                isBookmarking={bookmarkMutation.isPending}
              />
            ))}
          </div>
        </div>
      ) : (
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">All</TabsTrigger>
            {categories.map(category => (
              <TabsTrigger key={category} value={category} className="capitalize">
                {category}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <TabsContent value="all" className="space-y-4">
            {categories.map(category => (
              <div key={category} className="space-y-4">
                <h3 className="text-xl font-semibold capitalize">{category} Skills</h3>
                <ScrollArea className="w-full">
                  <div className="flex gap-4 pb-4">
                    {(groupedPlaylist[category] || []).slice(0, 4).map((item: CommonPlaylistItem) => (
                      <div key={item.id} className="flex-shrink-0 w-80">
                        <PlaylistItemCard
                          item={item}
                          isSelected={selectedItems.includes(item.id)}
                          onSelect={() => handleItemSelect(item.id)}
                          onAddToPlan={(planId) => addToPlanMutation.mutate({ planId, playlistItemId: item.id })}
                          onBookmark={() => bookmarkMutation.mutate(item.id)}
                          userPlans={userPlans}
                          getDifficultyColor={getDifficultyColor}
                          isAddingToPlan={addToPlanMutation.isPending}
                          isBookmarking={bookmarkMutation.isPending}
                        />
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            ))}
          </TabsContent>
          
          {categories.map(category => (
            <TabsContent key={category} value={category} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {(groupedPlaylist[category] || []).map((item: CommonPlaylistItem) => (
                  <PlaylistItemCard
                    key={item.id}
                    item={item}
                    isSelected={selectedItems.includes(item.id)}
                    onSelect={() => handleItemSelect(item.id)}
                    onAddToPlan={(planId) => addToPlanMutation.mutate({ planId, playlistItemId: item.id })}
                    onBookmark={() => bookmarkMutation.mutate(item.id)}
                    userPlans={userPlans}
                    getDifficultyColor={getDifficultyColor}
                    isAddingToPlan={addToPlanMutation.isPending}
                    isBookmarking={bookmarkMutation.isPending}
                  />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}

interface PlaylistItemCardProps {
  item: CommonPlaylistItem;
  isSelected: boolean;
  onSelect: () => void;
  onAddToPlan: (planId: number) => void;
  onBookmark: () => void;
  userPlans: LearningPlan[];
  getDifficultyColor: (level: string) => string;
  isAddingToPlan: boolean;
  isBookmarking: boolean;
}

function PlaylistItemCard({
  item,
  isSelected,
  onSelect,
  onAddToPlan,
  onBookmark,
  userPlans,
  getDifficultyColor,
  isAddingToPlan,
  isBookmarking
}: PlaylistItemCardProps) {
  const [showAddToPlans, setShowAddToPlans] = useState(false);

  return (
    <Card className={`cursor-pointer transition-all hover:shadow-md ${
      isSelected ? 'ring-2 ring-primary' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1">
            <CardTitle className="text-base leading-tight">{item.title}</CardTitle>
            <CardDescription className="text-sm mt-1 line-clamp-2">
              {item.description}
            </CardDescription>
          </div>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
          />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-3">
        <div className="flex flex-wrap gap-2">
          <Badge className={getDifficultyColor(item.difficultyLevel || 'beginner')}>
            {item.difficultyLevel || 'beginner'}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {item.category}
          </Badge>
        </div>
        
        {item.estimatedDuration && (
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="h-4 w-4 mr-1" />
            {item.estimatedDuration} min
          </div>
        )}
        
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {item.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {item.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{item.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
        
        <div className="flex gap-2">
          <Dialog open={showAddToPlans} onOpenChange={setShowAddToPlans}>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline" className="flex-1">
                <BookOpen className="h-4 w-4 mr-1" />
                Add to Plan
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add to Learning Plan</DialogTitle>
                <DialogDescription>
                  Choose which plan to add "{item.title}" to.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {userPlans.map((plan) => (
                  <Button
                    key={plan.id}
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                      onAddToPlan(plan.id);
                      setShowAddToPlans(false);
                    }}
                    disabled={isAddingToPlan}
                  >
                    {plan.title}
                  </Button>
                ))}
                {userPlans.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No learning plans found. Create one first!
                  </p>
                )}
              </div>
            </DialogContent>
          </Dialog>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={onBookmark}
            disabled={isBookmarking}
          >
            <BookmarkPlus className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}