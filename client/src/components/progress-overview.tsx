import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

type WeeklyProgress = {
  dailyHours: number[];
  totalHours: number;
  completedVideos: number;
};

export default function ProgressOverview() {
  const { user } = useAuth();
  
  const { data: weeklyProgress } = useQuery<WeeklyProgress>({
    queryKey: ["/api/analytics/weekly"],
    enabled: !!user,
  });

  // Generate mock chart data for the week
  const generateWeeklyChart = () => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const dailyHours = weeklyProgress?.dailyHours || [1.2, 0.8, 1.6, 1.4, 1.8, 0.4, 0.6];
    const maxHours = Math.max(...dailyHours);
    
    return days.map((day, index) => ({
      day,
      hours: dailyHours[index] || 0,
      percentage: maxHours > 0 ? (dailyHours[index] / maxHours) * 100 : 0,
    }));
  };

  const chartData = generateWeeklyChart();

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">
          This Week's Progress
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Weekly Chart */}
        <div className="mb-6">
          <div className="flex items-end justify-between h-32 space-x-2">
            {chartData.map((data, index) => (
              <div key={data.day} className="flex flex-col items-center space-y-2 flex-1">
                <div 
                  className={`w-full rounded-t transition-all duration-500 ${
                    index < 5 ? 'bg-blue-500' : 'bg-slate-300'
                  }`}
                  style={{ height: `${Math.max(data.percentage, 10)}%` }}
                  title={`${data.hours} hours`}
                />
                <span className="text-xs text-slate-500">{data.day}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-800">
              {weeklyProgress?.totalHours || 0}
            </div>
            <div className="text-xs text-slate-500">Hours This Week</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-800">
              {weeklyProgress?.completedVideos || 0}
            </div>
            <div className="text-xs text-slate-500">Videos Completed</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
