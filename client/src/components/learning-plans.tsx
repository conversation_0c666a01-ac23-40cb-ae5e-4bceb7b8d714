import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Code, BarChart, Plus, BookOpen } from "lucide-react";
import CreatePlanModal from "@/components/create-plan-modal";

interface LearningPlan {
  id: number;
  title: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  videoCount?: number;
  estimatedTime?: string;
  progress?: number;
  category?: string;
}

const categoryIcons = {
  'Web Development': Code,
  'Data Science': BarChart,
  'Programming': Code,
  default: BookOpen,
};

const categoryColors = {
  'Web Development': 'blue',
  'Data Science': 'violet',
  'Programming': 'emerald',
  default: 'slate',
};

export default function LearningPlans() {
  const { user } = useAuth();
  
  const { data: plans = [], isLoading } = useQuery<LearningPlan[]>({
    queryKey: ["/api/learning-plans"],
    enabled: !!user,
  });



  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-slate-800">My Learning Plans</h3>
        </div>
        <div className="grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-6 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-4 bg-slate-200 rounded w-full"></div>
                  <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                  <div className="h-2 bg-slate-200 rounded"></div>
                  <div className="h-10 bg-slate-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-slate-800">My Learning Plans</h3>
        <CreatePlanModal>
          <Button 
            variant="outline" 
            size="sm"
            className="text-blue-500 border-blue-500 hover:bg-blue-50"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Plan
          </Button>
        </CreatePlanModal>
      </div>

      {plans.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <div className="text-slate-400 mb-4">
              <BookOpen className="w-12 h-12 mx-auto" />
            </div>
            <h4 className="text-lg font-medium text-slate-800 mb-2">No learning plans yet</h4>
            <p className="text-slate-600 mb-4">
              Create your first learning plan to organize your educational journey.
            </p>
            <CreatePlanModal>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Plan
              </Button>
            </CreatePlanModal>
          </CardContent>
        </Card>
      ) : (
        <div className="grid sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-6">
          {plans.slice(0, 4).map((plan: LearningPlan) => {
            const IconComponent = categoryIcons[plan.category as keyof typeof categoryIcons] || categoryIcons.default;
            const colorClass = categoryColors[plan.category as keyof typeof categoryColors] || categoryColors.default;
            
            return (
              <Card key={plan.id} className="border border-slate-200 hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <Link href={`/plan/${plan.id}`}>
                        <h4 className="font-semibold text-slate-800 mb-2 cursor-pointer hover:text-blue-600 transition-colors">
                          {plan.title}
                        </h4>
                      </Link>
                      <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                        {plan.description}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-slate-500">
                        <span>{plan.videoCount || 0} videos</span>
                        <span>{plan.estimatedTime || '~0 hours'}</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      <div className={`w-12 h-12 bg-${colorClass}-100 rounded-lg flex items-center justify-center`}>
                        <IconComponent className={`w-6 h-6 text-${colorClass}-500`} />
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-slate-600">Progress</span>
                      <span className="font-medium text-slate-800">
                        {plan.progress || 0}%
                      </span>
                    </div>
                    <Progress 
                      value={plan.progress || 0} 
                      className="h-2"
                    />
                  </div>
                  
                  <Link href={`/plan/${plan.id}`}>
                    <Button 
                      className={`w-full bg-${colorClass}-500 hover:bg-${colorClass}-600 text-white`}
                    >
                      Continue Learning
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
