import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Sparkles,
  TrendingUp,
  Clock,
  Star,
  Play,
  BookOpen,
  Users,
  Zap,
  Target,
  Heart,
  Coffee,
  Moon,
  Sun,
  Lightbulb,
  Flame,
  Leaf,
  Compass,
} from "lucide-react";

// Mood categories with icons and descriptions
const MOOD_CATEGORIES = [
  {
    id: "focused",
    name: "Focused Learning",
    icon: Target,
    color: "blue",
    description: "Deep-dive content for concentrated study sessions",
    keywords: ["tutorial", "course", "lesson", "guide", "learn"],
  },
  {
    id: "creative",
    name: "Creative Spark",
    icon: Lightbulb,
    color: "yellow",
    description: "Inspiring content to boost creativity and innovation",
    keywords: ["design", "art", "creative", "inspiration", "innovation"],
  },
  {
    id: "motivational",
    name: "Motivational Boost",
    icon: Flame,
    color: "orange",
    description: "Energizing content to get you motivated",
    keywords: ["motivation", "success", "inspiration", "goals", "achievement"],
  },
  {
    id: "relaxing",
    name: "Chill & Learn",
    icon: Leaf,
    color: "green",
    description: "Gentle, easy-to-digest content for relaxed learning",
    keywords: ["basics", "introduction", "overview", "gentle", "easy"],
  },
  {
    id: "trending",
    name: "What's Hot",
    icon: TrendingUp,
    color: "red",
    description: "Trending topics and latest developments",
    keywords: ["trending", "latest", "new", "2024", "2025", "recent"],
  },
  {
    id: "discovery",
    name: "Explore New",
    icon: Compass,
    color: "purple",
    description: "Discover new subjects and expand your horizons",
    keywords: ["introduction", "beginner", "explore", "discover", "new"],
  },
];

// Time-based recommendations
const TIME_CONTEXTS = [
  { id: "morning", name: "Morning Focus", icon: Sun, time: [6, 12] },
  {
    id: "afternoon",
    name: "Afternoon Deep Dive",
    icon: Coffee,
    time: [12, 17],
  },
  { id: "evening", name: "Evening Wind Down", icon: Moon, time: [17, 23] },
  { id: "late", name: "Late Night Learning", icon: Star, time: [23, 6] },
];

interface MoodRecommendation {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  viewCount?: number;
  publishedAt: string;
  relevanceScore: number;
  moodScore: number;
  category: string;
  tags: string[];
}

interface UserMoodProfile {
  currentMood: string;
  preferredLearningTime: string;
  recentActivity: string[];
  skillLevel: string;
  interests: string[];
  completionRate: number;
  avgWatchTime: number;
}

export default function AIMoodBoard() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [selectedMood, setSelectedMood] = useState<string>("focused");
  const [currentTimeContext, setCurrentTimeContext] = useState<string>("");

  // Determine current time context
  useEffect(() => {
    const now = new Date();
    const hour = now.getHours();
    const context = TIME_CONTEXTS.find((ctx) =>
      ctx.time[0] <= ctx.time[1]
        ? hour >= ctx.time[0] && hour < ctx.time[1]
        : hour >= ctx.time[0] || hour < ctx.time[1]
    );
    setCurrentTimeContext(context?.id || "morning");
  }, []);

  // Get user's mood profile
  const { data: moodProfile } = useQuery({
    queryKey: ["/api/user/mood-profile"],
    enabled: isAuthenticated,
    retry: false,
  });

  // Get mood-based recommendations
  const { data: recommendations = [], isLoading } = useQuery({
    queryKey: ["/api/recommendations/mood", selectedMood, currentTimeContext],
    queryFn: async () => {
      const response = await fetch(
        `/api/recommendations/mood/${selectedMood}/${currentTimeContext}`,
        {
          credentials: "include",
        }
      );
      if (!response.ok) {
        throw new Error(`${response.status}: ${response.statusText}`);
      }
      return response.json();
    },
    enabled: Boolean(isAuthenticated && selectedMood && currentTimeContext),
    retry: false,
  });

  // Get AI insights
  const { data: aiInsights } = useQuery({
    queryKey: ["/api/ai-insights"],
    enabled: isAuthenticated,
    retry: false,
  });

  // Add to learning plan mutation
  const addToPlanMutation = useMutation({
    mutationFn: async (video: MoodRecommendation) => {
      const response = await apiRequest("POST", "/api/videos", {
        youtubeId: video.youtubeId,
        title: video.title,
        description: video.description,
        thumbnailUrl: video.thumbnailUrl,
        duration: video.duration,
        channelTitle: video.channelTitle,
        viewCount: video.viewCount,
        publishedAt: video.publishedAt,
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Added to Library",
        description: "Video saved to your learning library",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "Please log in to save videos",
          variant: "destructive",
        });
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save video",
        variant: "destructive",
      });
    },
  });

  if (!isAuthenticated) {
    return (
      <Card className="p-8 text-center">
        <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          AI Mood Board
        </h3>
        <p className="text-gray-600 mb-4">
          Sign in to get personalized content recommendations based on your mood
          and learning style.
        </p>
        <Button onClick={() => (window.location.href = "/api/login")}>
          Sign In
        </Button>
      </Card>
    );
  }

  const selectedCategory = MOOD_CATEGORIES.find(
    (cat) => cat.id === selectedMood
  );
  const timeContext = TIME_CONTEXTS.find(
    (ctx) => ctx.id === currentTimeContext
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">AI Mood Board</h2>
            <p className="text-gray-600">
              Personalized recommendations based on your current mood
            </p>
          </div>
        </div>
        {timeContext && (
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <timeContext.icon className="w-4 h-4" />
            <span>{timeContext.name}</span>
          </div>
        )}
      </div>

      {/* AI Insights */}
      {aiInsights && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="p-1 bg-blue-100 rounded-full">
                <Sparkles className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-blue-900 mb-1">AI Insights</h3>
                <p className="text-sm text-blue-800">
                  Based on your learning patterns, you're most productive during{" "}
                  {aiInsights.bestTime}
                  and prefer {aiInsights.preferredStyle} content. Your
                  completion rate is {aiInsights.completionRate}%.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mood Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-500" />
            What's Your Learning Mood?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {MOOD_CATEGORIES.map((category) => {
              const isSelected = selectedMood === category.id;
              const IconComponent = category.icon;

              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedMood(category.id)}
                  className={`p-4 rounded-lg border-2 transition-all text-center hover:shadow-md ${
                    isSelected
                      ? `border-${category.color}-500 bg-${category.color}-50`
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <IconComponent
                    className={`w-6 h-6 mx-auto mb-2 ${
                      isSelected
                        ? `text-${category.color}-600`
                        : "text-gray-500"
                    }`}
                  />
                  <div className="text-sm font-medium text-gray-900">
                    {category.name}
                  </div>
                  <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                    {category.description}
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {selectedCategory && <selectedCategory.icon className="w-5 h-5" />}
            {selectedCategory?.name} Recommendations
            <Badge variant="outline" className="ml-2">
              AI Curated
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="animate-pulse">
                  <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                  <CardContent className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : recommendations.length === 0 ? (
            <div className="text-center py-8">
              <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No recommendations yet
              </h3>
              <p className="text-gray-600">
                Try a different mood or complete more videos to improve AI
                recommendations.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Top recommendation */}
              {recommendations[0] && (
                <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                  <CardContent className="p-0">
                    <div className="flex flex-col lg:flex-row">
                      <div className="lg:w-1/3">
                        <div className="aspect-video relative overflow-hidden rounded-l-lg">
                          <img
                            src={recommendations[0].thumbnailUrl}
                            alt={recommendations[0].title}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 left-2">
                            <Badge className="bg-purple-600 hover:bg-purple-700">
                              <Star className="w-3 h-3 mr-1" />
                              Top Pick
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="lg:w-2/3 p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                              {recommendations[0].title}
                            </h3>
                            <p className="text-gray-600 mb-2 line-clamp-2">
                              {recommendations[0].description}
                            </p>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Users className="w-4 h-4" />
                                {recommendations[0].channelTitle}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                {recommendations[0].duration}
                              </span>
                              <span className="flex items-center gap-1">
                                <Zap className="w-4 h-4" />
                                {Math.round(recommendations[0].moodScore * 100)}
                                % match
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            className="flex-1 bg-purple-600 hover:bg-purple-700"
                            onClick={() =>
                              window.open(
                                `/video/${recommendations[0].id}`,
                                "_blank"
                              )
                            }
                          >
                            <Play className="w-4 h-4 mr-2" />
                            Watch Now
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() =>
                              addToPlanMutation.mutate(recommendations[0])
                            }
                            disabled={addToPlanMutation.isPending}
                          >
                            <BookOpen className="w-4 h-4 mr-2" />
                            Save
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Other recommendations */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recommendations.slice(1).map((video: MoodRecommendation) => (
                  <Card
                    key={video.id}
                    className="overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    <div className="aspect-video relative">
                      <img
                        src={video.thumbnailUrl}
                        alt={video.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="text-xs">
                          {Math.round(video.moodScore * 100)}% match
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">
                        {video.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {video.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span>{video.channelTitle}</span>
                        <span>{video.duration}</span>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          className="flex-1"
                          onClick={() =>
                            window.open(`/video/${video.id}`, "_blank")
                          }
                        >
                          <Play className="w-3 h-3 mr-1" />
                          Watch
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addToPlanMutation.mutate(video)}
                          disabled={addToPlanMutation.isPending}
                        >
                          <BookOpen className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Learning Patterns */}
      {moodProfile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-500" />
              Your Learning Patterns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {moodProfile.completionRate}%
                </div>
                <div className="text-sm text-blue-800">Completion Rate</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {moodProfile.avgWatchTime}m
                </div>
                <div className="text-sm text-green-800">Avg Watch Time</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {moodProfile.interests.length}
                </div>
                <div className="text-sm text-purple-800">Interests</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {moodProfile.skillLevel}
                </div>
                <div className="text-sm text-orange-800">Skill Level</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
