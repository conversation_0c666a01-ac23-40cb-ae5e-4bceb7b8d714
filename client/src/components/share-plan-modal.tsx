import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Copy, Share, Check, ExternalLink } from "lucide-react";

interface SharePlanModalProps {
  children: React.ReactNode;
  planId: number;
  planTitle: string;
}

export default function SharePlanModal({ children, planId, planTitle }: SharePlanModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState<string>("");
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const generateShareLinkMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", `/api/learning-plans/${planId}/share`);
      return response.json();
    },
    onSuccess: (data) => {
      const fullUrl = `${window.location.origin}/shared/${data.shareToken}`;
      setShareUrl(fullUrl);
      toast({
        title: "Share Link Generated",
        description: "Your learning plan is now ready to share!",
      });
    },
    onError: (error) => {
      console.error("Error generating share link:", error);
      toast({
        title: "Share Error",
        description: "Failed to generate share link. Please try again.",
        variant: "destructive",
      });
    },
  });

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast({
        title: "Copied!",
        description: "Share link copied to clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy link. Please select and copy manually.",
        variant: "destructive",
      });
    }
  };

  const openInNewTab = () => {
    window.open(shareUrl, '_blank');
  };

  const handleModalOpen = (open: boolean) => {
    setIsOpen(open);
    if (open && !shareUrl) {
      generateShareLinkMutation.mutate();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleModalOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share className="w-5 h-5" />
            Share Learning Plan
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium text-slate-700">Plan Title</Label>
            <div className="mt-1 p-2 bg-slate-50 rounded-md">
              <p className="text-sm font-medium">{planTitle}</p>
            </div>
          </div>

          {generateShareLinkMutation.isPending && (
            <div className="flex items-center justify-center py-6">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
              <span className="text-sm text-slate-600">Generating share link...</span>
            </div>
          )}

          {shareUrl && (
            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium text-slate-700">Share Link</Label>
                <div className="mt-1 flex gap-2">
                  <Input
                    value={shareUrl}
                    readOnly
                    className="text-sm"
                    onClick={(e) => e.currentTarget.select()}
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={copyToClipboard}
                    className="shrink-0"
                  >
                    {copied ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Public Access
                </Badge>
                <span className="text-xs text-slate-500">
                  Anyone with this link can view and copy your learning plan
                </span>
              </div>

              <div className="flex gap-2 pt-2">
                <Button onClick={openInNewTab} variant="outline" size="sm" className="flex-1">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button onClick={copyToClipboard} size="sm" className="flex-1">
                  {copied ? (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy Link
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {generateShareLinkMutation.isError && (
            <div className="text-center py-4">
              <p className="text-sm text-red-600 mb-3">Failed to generate share link</p>
              <Button 
                onClick={() => generateShareLinkMutation.mutate()} 
                variant="outline" 
                size="sm"
              >
                Try Again
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}