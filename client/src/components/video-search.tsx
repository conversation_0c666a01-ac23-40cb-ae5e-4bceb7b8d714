import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Clock, Eye } from "lucide-react";

interface YouTubeVideo {
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  duration?: string;
  viewCount?: number;
}

export default function VideoSearch() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<YouTubeVideo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const searchMutation = useMutation({
    mutationFn: async (query: string) => {
      const response = await apiRequest("GET", `/api/youtube/search?q=${encodeURIComponent(query)}&maxResults=6`);
      return await response.json();
    },
    onSuccess: (results) => {
      setSearchResults(results);
      setIsSearching(false);
    },
    onError: (error) => {
      setIsSearching(false);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Search Error",
        description: "Failed to search videos. Please try again.",
        variant: "destructive",
      });
    },
  });

  const addVideoMutation = useMutation({
    mutationFn: async (video: YouTubeVideo) => {
      // First create the video
      const videoResponse = await apiRequest("POST", "/api/videos", video);
      const createdVideo = await videoResponse.json();
      
      // TODO: Add to selected learning plan
      // For now, just show success message
      return createdVideo;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Video added to your library",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to add video. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSearch = useCallback((query: string) => {
    if (query.trim()) {
      setIsSearching(true);
      searchMutation.mutate(query.trim());
    } else {
      setSearchResults([]);
    }
  }, [searchMutation]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(value);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch(searchQuery);
    }
  };

  const formatViewCount = (count?: number) => {
    if (!count) return "0 views";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M views`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K views`;
    return `${count} views`;
  };

  const formatDuration = (duration?: string) => {
    if (!duration) return "Unknown";
    // Parse ISO 8601 duration (PT#M#S) to readable format
    const match = duration.match(/PT(?:(\d+)M)?(?:(\d+)S)?/);
    if (match) {
      const minutes = parseInt(match[1] || "0");
      const seconds = parseInt(match[2] || "0");
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    return duration;
  };

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-slate-800">
          Discover New Content
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search Bar */}
        <div className="relative mb-6">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="w-5 h-5 text-slate-400" />
          </div>
          <Input
            type="text"
            placeholder="Search YouTube videos to add to your learning plans..."
            value={searchQuery}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="pl-10 h-12"
          />
          {isSearching && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="space-y-4">
            {searchResults.map((video) => (
              <div 
                key={video.youtubeId} 
                className="flex space-x-4 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
              >
                <img 
                  src={video.thumbnailUrl || '/api/placeholder/200/150'} 
                  alt={video.title}
                  className="w-32 h-20 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-slate-800 mb-2 line-clamp-2">
                    {video.title}
                  </h4>
                  <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                    {video.description}
                  </p>
                  <div className="flex items-center space-x-4 text-xs text-slate-500">
                    <span>{video.channelTitle}</span>
                    {video.duration && (
                      <div className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDuration(video.duration)}
                      </div>
                    )}
                    {video.viewCount && (
                      <div className="flex items-center">
                        <Eye className="w-3 h-3 mr-1" />
                        {formatViewCount(video.viewCount)}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <Button 
                    size="sm"
                    onClick={() => addVideoMutation.mutate(video)}
                    disabled={addVideoMutation.isPending}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add to Plan
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {searchQuery && !isSearching && searchResults.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No videos found for "{searchQuery}"</p>
            <p className="text-sm">Try different keywords or check your spelling</p>
          </div>
        )}

        {!searchQuery && (
          <div className="text-center py-8 text-slate-500">
            <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Search for educational videos to add to your learning plans</p>
            <p className="text-sm">Try searching for topics like "React tutorial" or "Python basics"</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
