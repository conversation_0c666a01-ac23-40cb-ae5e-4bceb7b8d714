import { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";

import WelcomeSection from "@/components/welcome-section";
import ContinueLearning from "@/components/continue-learning";
import LearningPlans from "@/components/learning-plans";
import VideoSearch from "@/components/video-search";
import ProgressOverview from "@/components/progress-overview";
import Achievements from "@/components/achievements";
import QuickActions from "@/components/quick-actions";
import AIMoodBoard from "@/components/ai-mood-board";

export default function Dashboard() {
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      {/* Main Content Layout */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-12 gap-8">
          
          {/* Main Content Area */}
          <div className="lg:col-span-8 space-y-8">
            <WelcomeSection />
            <AIMoodBoard />
            <ContinueLearning />
            <LearningPlans />
            <VideoSearch />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-4 space-y-6">
            <ProgressOverview />
            <Achievements />
            <QuickActions />
          </div>
        </div>
      </div>
    </>
  );
}
