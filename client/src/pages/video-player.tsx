import { useEffect, useState, useRef } from "react";
import { use<PERSON>ara<PERSON>, <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, CheckCircle, Play, Pause, Volume2, VolumeX, Maximize, RotateCcw } from "lucide-react";

interface Video {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: number;
}

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
}

export default function VideoPlayer() {
  const { id } = useParams();
  const videoId = parseInt(id!);
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const queryClient = useQueryClient();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: video, isLoading: videoLoading } = useQuery({
    queryKey: [`/api/videos/${videoId}`],
    enabled: isAuthenticated && !!videoId,
  });

  const { data: progress } = useQuery({
    queryKey: [`/api/video-progress/${videoId}`],
    enabled: isAuthenticated && !!videoId,
  });

  const updateProgressMutation = useMutation({
    mutationFn: async (progressData: { videoId: number; currentTime: number; isCompleted: boolean }) => {
      await apiRequest("PUT", "/api/video-progress", progressData);
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
    },
  });

  const markCompletedMutation = useMutation({
    mutationFn: async () => {
      if (!video || !video.id) {
        console.error("Video data:", video);
        throw new Error("Video not loaded properly");
      }
      
      console.log("Marking video complete:", {
        videoId: video.id,
        videoTitle: video.title,
        duration: duration
      });
      
      // Use video duration from video metadata or a default high value
      const completionTime = duration > 0 ? duration : 3600; // Default to 1 hour if duration not available
      
      const response = await apiRequest("PUT", "/api/video-progress", {
        videoId: video.id,
        currentTime: completionTime,
        isCompleted: true,
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/video-progress/${videoId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/continue-learning'] });
      queryClient.invalidateQueries({ queryKey: ['/api/analytics/daily'] });
      queryClient.invalidateQueries({ queryKey: ['/api/analytics/weekly'] });
      toast({
        title: "Congratulations!",
        description: "Video marked as completed",
      });
    },
    onError: (error) => {
      console.error("Mark complete error:", error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to mark video as complete. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Auto-hide controls
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    };

    if (showControls) {
      resetControlsTimeout();
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  // Save progress periodically
  useEffect(() => {
    if (currentTime > 0 && video?.id) {
      const interval = setInterval(() => {
        updateProgressMutation.mutate({
          videoId: video.id,
          currentTime,
          isCompleted: false,
        });
      }, 30000); // Save every 30 seconds

      return () => clearInterval(interval);
    }
  }, [currentTime, video?.id]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
    // In a real implementation, this would control the YouTube player
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    // In a real implementation, this would control the YouTube player
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const percent = (e.clientX - rect.left) / rect.width;
    const newTime = percent * duration;
    setCurrentTime(newTime);
    // In a real implementation, this would seek the YouTube player
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading || videoLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading video...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !video) {
    return null;
  }

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="min-h-screen bg-black">
      
      <div className="max-w-7xl mx-auto">
        {/* Video Player */}
        <div className="relative bg-black aspect-video">
          {/* YouTube Embed with controls enabled */}
          <iframe
            ref={iframeRef}
            className="w-full h-full"
            src={`https://www.youtube.com/embed/${video.youtubeId}?autoplay=1&rel=0&modestbranding=1&controls=1&fs=1&enablejsapi=1`}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={video.title}
          />
          
          {/* Simple Top Controls Overlay - Only for back button and mark complete */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-center z-10">
            <Link href="/">
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/20 bg-black/50">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </Link>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-white/20 bg-black/50"
              onClick={() => markCompletedMutation.mutate()}
              disabled={markCompletedMutation.isPending}
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              {markCompletedMutation.isPending ? "Marking..." : "Mark Complete"}
            </Button>
          </div>
        </div>

        {/* Video Info */}
        <div className="bg-white p-6">
          <div className="max-w-4xl">
            <h1 className="text-2xl font-bold text-slate-800 mb-2">{video.title}</h1>
            <div className="flex items-center space-x-4 text-sm text-slate-600 mb-4">
              <span>{video.channelTitle}</span>
              <span>•</span>
              <span>{video.viewCount?.toLocaleString()} views</span>
              <span>•</span>
              <span>{new Date(video.publishedAt).toLocaleDateString()}</span>
            </div>
            
            {progress && (
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-slate-600">Your Progress</span>
                  <span className="font-medium text-slate-800">
                    {progress.isCompleted ? 'Completed' : `${Math.round(progressPercentage)}%`}
                  </span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            )}
            
            <p className="text-slate-700 leading-relaxed">{video.description}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
