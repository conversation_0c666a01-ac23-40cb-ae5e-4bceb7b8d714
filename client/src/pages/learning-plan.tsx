import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import VideoCard from "@/components/video-card";
import AddVideoModal from "@/components/add-video-modal";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Play,
  MoreVertical,
  Plus,
  Share2,
  Copy,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface LearningPlan {
  id: number;
  title: string;
  description: string;
  slug: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  permissions?: {
    canView: boolean;
    canAddVideos: boolean;
    canShare: boolean;
    isOwner: boolean;
  };
}

interface PlanVideo {
  id: number;
  planId: number;
  videoId: number;
  orderIndex: number;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
}

export default function LearningPlan() {
  const { slug } = useParams();
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const queryClient = useQueryClient();

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: plan, isLoading: planLoading } = useQuery<LearningPlan>({
    queryKey: [`/api/learning-plans/slug/${slug}`],
    enabled: isAuthenticated && !!slug,
  });

  const { data: planVideos = [], isLoading: videosLoading } = useQuery<
    PlanVideo[]
  >({
    queryKey: [`/api/learning-plans/${plan?.id}/videos`],
    enabled: isAuthenticated && !!plan?.id,
  });

  const { data: progress = [] } = useQuery<VideoProgress[]>({
    queryKey: [`/api/progress/plan/${plan?.id}`],
    enabled: isAuthenticated && !!plan?.id,
  });

  const removeVideoMutation = useMutation({
    mutationFn: async (videoId: number) => {
      await apiRequest(
        "DELETE",
        `/api/learning-plans/${plan?.id}/videos/${videoId}`
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [`/api/learning-plans/${plan?.id}/videos`],
      });
      toast({
        title: "Success",
        description: "Video removed from learning plan",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to remove video",
        variant: "destructive",
      });
    },
  });

  const shareplanMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest(
        "POST",
        `/api/learning-plans/${plan?.id}/share`
      );
      return response.json();
    },
    onSuccess: (data) => {
      navigator.clipboard.writeText(data.shareUrl);
      toast({
        title: "Link Copied!",
        description: "Share link has been copied to your clipboard",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to generate share link",
        variant: "destructive",
      });
    },
  });

  if (isLoading || planLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading learning plan...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !plan) {
    return null;
  }

  const completedVideos = progress.filter(
    (p: VideoProgress) => p.isCompleted
  ).length;
  const totalVideos = planVideos.length;
  const progressPercentage =
    totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0;

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Plan Overview */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl mb-2">{plan.title}</CardTitle>
                <p className="text-slate-600 mb-4">{plan.description}</p>
                <div className="flex items-center space-x-6 text-sm text-slate-500">
                  <span>{totalVideos} videos</span>
                  <span>{completedVideos} completed</span>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareplanMutation.mutate()}
                  disabled={shareplanMutation.isPending}
                >
                  {shareplanMutation.isPending ? (
                    <div className="w-4 h-4 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin" />
                  ) : (
                    <Share2 className="w-4 h-4" />
                  )}
                  <span className="ml-2">Share</span>
                </Button>
                <div className="text-right">
                  <div className="text-2xl font-bold text-slate-800 mb-1">
                    {Math.round(progressPercentage)}%
                  </div>
                  <div className="text-sm text-slate-500">Complete</div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Progress value={progressPercentage} className="mb-4" />
            {totalVideos > 0 && (
              <div className="flex justify-between items-center">
                <Button
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                  asChild
                >
                  <Link href={`/video/${planVideos[0]?.video.id}`}>
                    <Play className="w-4 h-4 mr-2" />
                    Continue Learning
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Videos List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-slate-800">Videos</h3>
            {plan.permissions?.canAddVideos && (
              <AddVideoModal planId={plan.id}>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-blue-500 border-blue-500 hover:bg-blue-50"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Video
                </Button>
              </AddVideoModal>
            )}
          </div>

          {videosLoading ? (
            <div className="grid gap-4">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="bg-white rounded-xl border border-slate-200 p-4 animate-pulse"
                >
                  <div className="flex space-x-4">
                    <div className="w-32 h-20 bg-slate-200 rounded-lg"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                      <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                      <div className="h-3 bg-slate-200 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : planVideos.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <div className="text-slate-400 mb-4">
                  <Play className="w-12 h-12 mx-auto" />
                </div>
                <h4 className="text-lg font-medium text-slate-800 mb-2">
                  No videos yet
                </h4>
                <p className="text-slate-600 mb-4">
                  Start building your learning plan by adding educational
                  videos.
                </p>
                {plan.permissions?.canAddVideos && (
                  <AddVideoModal planId={plan.id}>
                    <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Video
                    </Button>
                  </AddVideoModal>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {planVideos.map((planVideo: PlanVideo) => {
                const videoProgress = progress.find(
                  (p: VideoProgress) => p.videoId === planVideo.video.id
                );

                return (
                  <Card
                    key={planVideo.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardContent className="p-4">
                      <div className="flex space-x-4">
                        <Link
                          href={`/video/${planVideo.video.id}`}
                          className="flex-shrink-0"
                        >
                          <img
                            src={
                              planVideo.video.thumbnailUrl ||
                              "/api/placeholder/320/180"
                            }
                            alt={planVideo.video.title}
                            className="w-32 h-20 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                          />
                        </Link>

                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-2">
                            <Link href={`/video/${planVideo.video.id}`}>
                              <h4 className="font-semibold text-slate-800 hover:text-blue-600 transition-colors cursor-pointer line-clamp-2">
                                {planVideo.video.title}
                              </h4>
                            </Link>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem
                                  onClick={() =>
                                    removeVideoMutation.mutate(
                                      planVideo.video.id
                                    )
                                  }
                                  className="text-red-600"
                                >
                                  Remove from plan
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>

                          <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                            {planVideo.video.description}
                          </p>

                          <div className="flex items-center space-x-4 text-xs text-slate-500 mb-3">
                            <span>{planVideo.video.channelTitle}</span>
                            <span>{planVideo.video.duration}</span>
                            {videoProgress && (
                              <span
                                className={
                                  videoProgress.isCompleted
                                    ? "text-green-600"
                                    : "text-blue-600"
                                }
                              >
                                {videoProgress.isCompleted
                                  ? "Completed"
                                  : `${Math.round(
                                      (videoProgress.currentTime / 100) * 100
                                    )}% watched`}
                              </span>
                            )}
                          </div>

                          {videoProgress && !videoProgress.isCompleted && (
                            <div className="w-full bg-slate-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${Math.min(
                                    (videoProgress.currentTime / 100) * 100,
                                    100
                                  )}%`,
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
