import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import VideoCard from "@/components/video-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Sparkles,
  TrendingUp,
  Clock,
  Play,
  Plus,
  Check,
} from "lucide-react";

interface Video {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: number;
}

interface YouTubeVideo {
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  duration?: string;
  viewCount?: number;
}

interface LearningPlan {
  id: number;
  title: string;
  description: string;
}

export default function Explore() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<YouTubeVideo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryResults, setCategoryResults] = useState<YouTubeVideo[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<string>("");
  const [isAddToPlanOpen, setIsAddToPlanOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<YouTubeVideo | null>(null);
  const [pageToken, setPageToken] = useState<string | null>(null);
  const [hasMoreResults, setHasMoreResults] = useState(false);

  // Get user's learning plans for the "Add to Plan" functionality
  const { data: userPlans = [] } = useQuery({
    queryKey: ["/api/learning-plans"],
    enabled: isAuthenticated,
  });

  // Get existing videos from the database
  const { data: existingVideos = [] } = useQuery({
    queryKey: ["/api/videos"],
  });

  // Real-time recommended videos from YouTube API
  const { data: recommendedVideos = [], isLoading: isLoadingRecommended } =
    useQuery({
      queryKey: ["/api/youtube/recommended"],
      queryFn: async () => {
        const topics = ["tutorial", "programming", "learning", "education"];
        const randomTopic = topics[Math.floor(Math.random() * topics.length)];
        const response = await apiRequest(
          "GET",
          `/api/youtube/search?q=${encodeURIComponent(randomTopic)}`
        );
        const data = await response.json();
        return (data.videos || data || []).slice(0, 6);
      },
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      enabled: isAuthenticated,
    });

  const searchMutation = useMutation({
    mutationFn: async ({
      query,
      pageToken,
    }: {
      query: string;
      pageToken?: string;
    }) => {
      if (pageToken) {
        setIsLoadingMore(true);
      } else {
        setIsSearching(true);
      }

      let url = `/api/youtube/search?q=${encodeURIComponent(query)}`;
      if (pageToken) {
        url += `&pageToken=${pageToken}`;
      }

      const response = await apiRequest("GET", url);
      return response.json();
    },
    onSuccess: (data) => {
      // If we have a pageToken, append the results, otherwise replace them
      if (pageToken) {
        setSearchResults((prev) => [...prev, ...(data.videos || [])]);
      } else {
        setSearchResults(data.videos || []);
      }

      // Store the next page token if available
      setPageToken(data.nextPageToken || null);
      setHasMoreResults(!!data.nextPageToken);
      setIsSearching(false);
      setIsLoadingMore(false);
    },
    onError: (error) => {
      console.error("Search error:", error);
      setIsSearching(false);
      setIsLoadingMore(false);
      toast({
        title: "Search Error",
        description: "Failed to search videos. Please try again.",
        variant: "destructive",
      });
    },
  });

  const categorySearchMutation = useMutation({
    mutationFn: async (category: string) => {
      const response = await apiRequest(
        "GET",
        `/api/youtube/search?q=${encodeURIComponent(category)}`
      );
      return response.json();
    },
    onSuccess: (data) => {
      setCategoryResults(data.videos || data || []);
    },
    onError: (error) => {
      console.error("Category search error:", error);
      toast({
        title: "Search Error",
        description: "Failed to load category videos. Please try again.",
        variant: "destructive",
      });
    },
  });

  const addToLibraryMutation = useMutation({
    mutationFn: async (video: YouTubeVideo) => {
      const response = await apiRequest("POST", "/api/videos", video);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
      toast({
        title: "Added to Library",
        description: "Video has been added to your library",
      });
    },
    onError: (error) => {
      console.error("Error adding video:", error);
      toast({
        title: "Error",
        description: "Failed to add video to library",
        variant: "destructive",
      });
    },
  });

  const addToPlanMutation = useMutation({
    mutationFn: async ({
      video,
      planId,
    }: {
      video: YouTubeVideo;
      planId: string;
    }) => {
      // First add video to library if not exists
      const videoResponse = await apiRequest("POST", "/api/videos", video);
      const videoData = await videoResponse.json();

      // Then add to the selected plan
      const planResponse = await apiRequest(
        "POST",
        `/api/learning-plans/${planId}/videos`,
        {
          videoId: videoData.id,
        }
      );
      return planResponse.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      queryClient.invalidateQueries({
        queryKey: [`/api/learning-plans/${variables.planId}/videos`],
      });
      setIsAddToPlanOpen(false);
      setSelectedVideo(null);
      setSelectedPlan("");
      toast({
        title: "Added to Plan",
        description: "Video has been added to your learning plan",
      });
    },
    onError: (error) => {
      console.error("Error adding video to plan:", error);
      toast({
        title: "Error",
        description: "Failed to add video to plan",
        variant: "destructive",
      });
    },
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSelectedCategory(null);
      setCategoryResults([]);
      // Reset page token when starting a new search
      setPageToken(null);
      searchMutation.mutate({ query: searchQuery });
    }
  };

  const handleLoadMore = () => {
    if (pageToken && searchQuery) {
      searchMutation.mutate({ query: searchQuery, pageToken });
    }
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    setSearchResults([]);
    setSearchQuery("");
    // Use more specific search terms for better results
    const searchTerms = {
      Programming: "programming tutorial beginner",
      "Data Science": "data science python tutorial",
      Design: "design tutorial ui ux",
      Business: "business management course",
    };
    const searchTerm =
      searchTerms[category as keyof typeof searchTerms] || category;
    categorySearchMutation.mutate(searchTerm);
  };

  const handleAddToPlan = (video: YouTubeVideo) => {
    // If user has no plans, show error
    if (!userPlans || userPlans.length === 0) {
      toast({
        title: "No Learning Plans",
        description: "Create a learning plan first to add videos.",
        variant: "destructive",
      });
      return;
    }

    // If user has only one plan, add directly
    if (userPlans.length === 1) {
      addToPlanMutation.mutate({ video, planId: userPlans[0].id.toString() });
      return;
    }

    // If user has multiple plans, show selection dialog
    setSelectedVideo(video);
    setIsAddToPlanOpen(true);
  };

  const handleConfirmAddToPlan = () => {
    if (selectedVideo && selectedPlan) {
      addToPlanMutation.mutate({ video: selectedVideo, planId: selectedPlan });
    }
  };

  const isVideoInLibrary = (youtubeId: string) => {
    return existingVideos.some((video: Video) => video.youtubeId === youtubeId);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-slate-800 mb-4">
          Explore Learning Content
        </h1>
        <p className="text-slate-600 mb-6">
          Discover new courses and educational videos to enhance your learning
          journey
        </p>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search for videos, courses, or topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-3 text-lg"
            />
            <Button
              type="submit"
              disabled={isSearching || !searchQuery.trim()}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 hover:bg-blue-600"
            >
              {isSearching ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                "Search"
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-slate-800 mb-6 flex items-center">
            <Search className="w-6 h-6 mr-2" />
            Search Results
            <span className="ml-2 text-sm font-normal text-gray-500">
              ({searchResults.length} videos)
            </span>
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {searchResults.map((video, index) => (
              <VideoSearchCard
                key={`${video.youtubeId}-${index}`}
                video={video}
                isInLibrary={isVideoInLibrary(video.youtubeId)}
                onAddToLibrary={() => addToLibraryMutation.mutate(video)}
                onAddToPlan={() => handleAddToPlan(video)}
                isAdding={
                  addToLibraryMutation.isPending || addToPlanMutation.isPending
                }
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMoreResults && (
            <div className="mt-8 text-center">
              <Button
                onClick={handleLoadMore}
                disabled={isLoadingMore}
                variant="outline"
                size="lg"
                className="px-8"
              >
                {isLoadingMore ? (
                  <>
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading more...
                  </>
                ) : (
                  <>Load More Videos</>
                )}
              </Button>
            </div>
          )}
        </section>
      )}

      {/* Category Results */}
      {selectedCategory && categoryResults.length > 0 && (
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-slate-800 mb-6 flex items-center">
            <Play className="w-6 h-6 mr-2" />
            {selectedCategory} Videos
            <Button
              variant="ghost"
              size="sm"
              className="ml-auto"
              onClick={() => {
                setSelectedCategory(null);
                setCategoryResults([]);
              }}
            >
              Clear
            </Button>
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoryResults.map((video, index) => (
              <VideoSearchCard
                key={`${video.youtubeId}-${index}`}
                video={video}
                isInLibrary={isVideoInLibrary(video.youtubeId)}
                onAddToLibrary={() => addToLibraryMutation.mutate(video)}
                onAddToPlan={() => handleAddToPlan(video)}
                isAdding={
                  addToLibraryMutation.isPending || addToPlanMutation.isPending
                }
              />
            ))}
          </div>
        </section>
      )}

      {/* Hide other sections when showing category or search results */}
      {!selectedCategory && searchResults.length === 0 && (
        <>
          {/* Recommendations */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-slate-800 mb-6 flex items-center">
              <Sparkles className="w-6 h-6 mr-2" />
              Recommended for You
            </h2>
            {isLoadingRecommended ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="bg-slate-200 h-40 rounded-md mb-3"></div>
                      <div className="bg-slate-200 h-4 rounded mb-2"></div>
                      <div className="bg-slate-200 h-3 rounded mb-4"></div>
                      <div className="bg-slate-200 h-8 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {recommendedVideos.map((video, index) => (
                  <VideoRecommendationCard
                    key={`${video.youtubeId}-${index}`}
                    video={video}
                    isInLibrary={isVideoInLibrary(video.youtubeId)}
                    onAddToLibrary={() => addToLibraryMutation.mutate(video)}
                    onAddToPlan={() => handleAddToPlan(video)}
                    isAdding={
                      addToLibraryMutation.isPending ||
                      addToPlanMutation.isPending
                    }
                  />
                ))}
              </div>
            )}
          </section>

          {/* Trending Topics */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-slate-800 mb-6 flex items-center">
              <TrendingUp className="w-6 h-6 mr-2" />
              Trending Topics
            </h2>
            <div className="flex flex-wrap gap-3">
              {[
                "Machine Learning",
                "Web Development",
                "Data Science",
                "Mobile Development",
                "Cloud Computing",
                "Cybersecurity",
                "UI/UX Design",
                "DevOps",
              ].map((topic) => (
                <Badge
                  key={topic}
                  variant="outline"
                  className="cursor-pointer hover:bg-blue-50 hover:border-blue-300 px-4 py-2"
                  onClick={() => {
                    setSearchQuery(topic);
                    setSelectedCategory(null);
                    setCategoryResults([]);
                    searchMutation.mutate(topic);
                  }}
                >
                  {topic}
                </Badge>
              ))}
            </div>
          </section>

          {/* Browse Categories */}
          <section>
            <h2 className="text-2xl font-semibold text-slate-800 mb-6">
              Browse by Category
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                {
                  name: "Programming",
                  count: "120+ courses",
                  color: "bg-blue-500",
                },
                {
                  name: "Data Science",
                  count: "85+ courses",
                  color: "bg-green-500",
                },
                {
                  name: "Design",
                  count: "60+ courses",
                  color: "bg-purple-500",
                },
                {
                  name: "Business",
                  count: "95+ courses",
                  color: "bg-orange-500",
                },
              ].map((category) => (
                <Card
                  key={category.name}
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => handleCategoryClick(category.name)}
                >
                  <CardContent className="p-6 text-center">
                    <div
                      className={`w-12 h-12 ${category.color} rounded-lg mx-auto mb-4 flex items-center justify-center`}
                    >
                      <Play className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-slate-800 mb-1">
                      {category.name}
                    </h3>
                    <p className="text-sm text-slate-600">{category.count}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </>
      )}

      {/* Plan Selection Dialog */}
      <Dialog open={isAddToPlanOpen} onOpenChange={setIsAddToPlanOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add to Learning Plan</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-slate-600">
              Select a learning plan to add "{selectedVideo?.title}" to:
            </p>
            <Select value={selectedPlan} onValueChange={setSelectedPlan}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a learning plan" />
              </SelectTrigger>
              <SelectContent>
                {userPlans.map((plan: any) => (
                  <SelectItem key={plan.id} value={plan.id.toString()}>
                    {plan.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setIsAddToPlanOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={handleConfirmAddToPlan}
                disabled={!selectedPlan || addToPlanMutation.isPending}
              >
                {addToPlanMutation.isPending ? "Adding..." : "Add to Plan"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface VideoSearchCardProps {
  video: YouTubeVideo;
  isInLibrary: boolean;
  onAddToLibrary: () => void;
  onAddToPlan: () => void;
  isAdding: boolean;
}

function VideoSearchCard({
  video,
  isInLibrary,
  onAddToLibrary,
  onAddToPlan,
  isAdding,
}: VideoSearchCardProps) {
  return (
    <Card className="group hover:shadow-lg transition-all duration-200">
      <CardContent className="p-4">
        <div className="relative mb-3">
          <img
            src={video.thumbnailUrl}
            alt={video.title}
            className="w-full h-40 rounded-md object-cover"
          />
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {video.duration || "N/A"}
          </div>
        </div>
        <h3 className="font-medium text-slate-800 mb-2 line-clamp-2">
          {video.title}
        </h3>
        <p className="text-sm text-slate-600 mb-2">{video.channelTitle}</p>
        <p className="text-xs text-slate-500 mb-3 line-clamp-2">
          {video.description}
        </p>
        <div className="flex gap-2">
          <Button
            onClick={onAddToLibrary}
            disabled={isInLibrary || isAdding}
            variant="outline"
            className="flex-1"
          >
            {isInLibrary ? (
              <>
                <Check className="w-4 h-4 mr-2" />
                Added
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Library
              </>
            )}
          </Button>
          <Button onClick={onAddToPlan} disabled={isAdding} className="flex-1">
            {isAdding ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            Add to Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface VideoRecommendationCardProps {
  video: {
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration?: string;
    channelTitle: string;
    viewCount?: number;
  };
  isInLibrary: boolean;
  onAddToLibrary: () => void;
  onAddToPlan: () => void;
  isAdding: boolean;
}

function VideoRecommendationCard({
  video,
  isInLibrary,
  onAddToLibrary,
  onAddToPlan,
  isAdding,
}: VideoRecommendationCardProps) {
  return (
    <Card className="group hover:shadow-lg transition-all duration-200">
      <CardContent className="p-4">
        <div className="relative mb-3">
          <img
            src={video.thumbnailUrl}
            alt={video.title}
            className="w-full h-40 rounded-md object-cover"
          />
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {video.duration || "N/A"}
          </div>
          <Badge className="absolute top-2 left-2 bg-yellow-500 text-yellow-900">
            Recommended
          </Badge>
        </div>
        <h3 className="font-medium text-slate-800 mb-2 line-clamp-2">
          {video.title}
        </h3>
        <p className="text-sm text-slate-600 mb-1">{video.channelTitle}</p>
        <p className="text-xs text-slate-500 mb-3">
          {video.viewCount
            ? `${video.viewCount.toLocaleString()} views`
            : "Educational content"}
        </p>
        <div className="flex gap-2">
          <Button
            onClick={onAddToLibrary}
            disabled={isInLibrary || isAdding}
            variant="outline"
            className="flex-1"
          >
            {isInLibrary ? (
              <>
                <Check className="w-4 h-4 mr-2" />
                Added
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Library
              </>
            )}
          </Button>
          <Button onClick={onAddToPlan} disabled={isAdding} className="flex-1">
            {isAdding ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            Add to Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
