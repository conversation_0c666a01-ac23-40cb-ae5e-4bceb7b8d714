import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import FriendsNetwork from "@/components/friends-network";
import LearningPathVisualizer from "@/components/learning-path-visualizer";
import DifficultyOptimizer from "@/components/difficulty-optimizer";
import CommonPlaylist from "@/components/common-playlist";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Play,
  Plus,
  Trash2,
  BookO<PERSON>,
  Users,
  Target,
  Brain,
  List
} from "lucide-react";
import { useState } from "react";

interface Video {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: number;
}

interface LearningPlan {
  id: number;
  title: string;
  description: string;
}

export default function Library() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isAddToPlanOpen, setIsAddToPlanOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>("");

  // Get all videos from library
  const { data: videos = [], isLoading } = useQuery({
    queryKey: ['/api/videos'],
    enabled: isAuthenticated,
  });

  // Get user's learning plans for the "Add to Plan" functionality
  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
    enabled: isAuthenticated,
  });

  // Filter videos based on search query
  const filteredVideos = videos.filter((video: Video) =>
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.channelTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const addToPlanMutation = useMutation({
    mutationFn: async ({ video, planId }: { video: Video; planId: string }) => {
      const planResponse = await apiRequest("POST", `/api/learning-plans/${planId}/videos`, {
        videoId: video.id
      });
      return planResponse.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
      queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/${variables.planId}/videos`] });
      setIsAddToPlanOpen(false);
      setSelectedVideo(null);
      setSelectedPlan("");
      toast({
        title: "Added to Plan",
        description: "Video has been added to your learning plan",
      });
    },
    onError: (error) => {
      console.error("Error adding video to plan:", error);
      toast({
        title: "Error",
        description: "Failed to add video to plan",
        variant: "destructive",
      });
    },
  });

  const deleteVideoMutation = useMutation({
    mutationFn: async (videoId: number) => {
      const response = await apiRequest("DELETE", `/api/videos/${videoId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/videos'] });
      toast({
        title: "Video Removed",
        description: "Video has been removed from your library",
      });
    },
    onError: (error) => {
      console.error("Error deleting video:", error);
      toast({
        title: "Error",
        description: "Failed to remove video from library",
        variant: "destructive",
      });
    },
  });

  const handleAddToPlan = (video: Video) => {
    // If user has no plans, show error
    if (!userPlans || userPlans.length === 0) {
      toast({
        title: "No Learning Plans",
        description: "Create a learning plan first to add videos.",
        variant: "destructive",
      });
      return;
    }

    // If user has only one plan, add directly
    if (userPlans.length === 1) {
      addToPlanMutation.mutate({ video, planId: userPlans[0].id.toString() });
      return;
    }

    // If user has multiple plans, show selection dialog
    setSelectedVideo(video);
    setIsAddToPlanOpen(true);
  };

  const handleConfirmAddToPlan = () => {
    if (selectedVideo && selectedPlan) {
      addToPlanMutation.mutate({ video: selectedVideo, planId: selectedPlan });
    }
  };

  const handlePlayVideo = (youtubeId: string) => {
    window.open(`https://www.youtube.com/watch?v=${youtubeId}`, '_blank');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <CardContent>
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-slate-400" />
            <h2 className="text-xl font-semibold mb-2">Sign In Required</h2>
            <p className="text-slate-600 mb-4">Please sign in to view your video library.</p>
            <Button onClick={() => window.location.href = '/api/login'}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-4 flex items-center">
            <BookOpen className="w-8 h-8 mr-3" />
            Learning Hub
          </h1>
          <p className="text-slate-600 mb-6">
            Manage your videos, connect with friends, and visualize your learning journey
          </p>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="library" className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="library" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Video Library
            </TabsTrigger>
            <TabsTrigger value="playlist" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              Common Playlist
            </TabsTrigger>
            <TabsTrigger value="friends" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Friends Network
            </TabsTrigger>
            <TabsTrigger value="paths" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Learning Paths
            </TabsTrigger>
            <TabsTrigger value="difficulty" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI Optimizer
            </TabsTrigger>
          </TabsList>

          <TabsContent value="library" className="space-y-6">
            {/* Search Bar */}
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search your library..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="bg-slate-200 h-40 rounded-md mb-3"></div>
                      <div className="bg-slate-200 h-4 rounded mb-2"></div>
                      <div className="bg-slate-200 h-3 rounded mb-4"></div>
                      <div className="bg-slate-200 h-8 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && filteredVideos.length === 0 && (
              <Card className="p-12 text-center">
                <CardContent>
                  <BookOpen className="w-16 h-16 mx-auto mb-4 text-slate-400" />
                  <h2 className="text-xl font-semibold mb-2">
                    {searchQuery ? 'No videos found' : 'Your library is empty'}
                  </h2>
                  <p className="text-slate-600 mb-4">
                    {searchQuery 
                      ? 'Try searching with different keywords or clear your search.'
                      : 'Start exploring and adding videos to build your learning library.'
                    }
                  </p>
                  {!searchQuery && (
                    <Button onClick={() => window.location.href = '/explore'}>
                      Explore Videos
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Videos Grid */}
            {!isLoading && filteredVideos.length > 0 && (
              <>
                <div className="mb-4 text-sm text-slate-600">
                  {filteredVideos.length} video{filteredVideos.length !== 1 ? 's' : ''} in your library
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredVideos.map((video: Video) => (
                    <Card key={video.id} className="group hover:shadow-lg transition-all duration-200">
                      <CardContent className="p-4">
                        <div className="relative mb-3 cursor-pointer" onClick={() => handlePlayVideo(video.youtubeId)}>
                          <img
                            src={video.thumbnailUrl}
                            alt={video.title}
                            className="w-full h-40 rounded-md object-cover group-hover:opacity-90 transition-opacity"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-md transition-all flex items-center justify-center">
                            <Play className="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                            {video.duration || "N/A"}
                          </div>
                        </div>
                        <h3 className="font-medium text-slate-800 mb-2 line-clamp-2">
                          {video.title}
                        </h3>
                        <p className="text-sm text-slate-600 mb-3">
                          {video.channelTitle}
                        </p>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => handleAddToPlan(video)}
                            disabled={addToPlanMutation.isPending}
                            size="sm"
                            className="flex-1"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add to Plan
                          </Button>
                          <Button
                            onClick={() => deleteVideoMutation.mutate(video.id)}
                            disabled={deleteVideoMutation.isPending}
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="playlist" className="space-y-6">
            <CommonPlaylist />
          </TabsContent>

          <TabsContent value="friends" className="space-y-6">
            <FriendsNetwork />
          </TabsContent>

          <TabsContent value="paths" className="space-y-6">
            <div className="space-y-6">
              {userPlans && userPlans.length > 0 ? (
                userPlans.map((plan: LearningPlan) => (
                  <LearningPathVisualizer 
                    key={plan.id} 
                    planId={plan.id} 
                    planTitle={plan.title} 
                  />
                ))
              ) : (
                <Card className="p-12 text-center">
                  <CardContent>
                    <Target className="w-16 h-16 mx-auto mb-4 text-slate-400" />
                    <h2 className="text-xl font-semibold mb-2">No Learning Plans</h2>
                    <p className="text-slate-600 mb-4">
                      Create learning plans to visualize your learning paths and track progress.
                    </p>
                    <Button onClick={() => window.location.href = '/my-plans'}>
                      Create Learning Plan
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="difficulty" className="space-y-6">
            <DifficultyOptimizer />
          </TabsContent>
        </Tabs>

        {/* Plan Selection Dialog */}
        <Dialog open={isAddToPlanOpen} onOpenChange={setIsAddToPlanOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add to Learning Plan</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-slate-600">
                Select a learning plan to add "{selectedVideo?.title}" to:
              </p>
              <Select value={selectedPlan} onValueChange={setSelectedPlan}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a learning plan" />
                </SelectTrigger>
                <SelectContent>
                  {userPlans.map((plan: any) => (
                    <SelectItem key={plan.id} value={plan.id.toString()}>
                      {plan.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex gap-3">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setIsAddToPlanOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handleConfirmAddToPlan}
                  disabled={!selectedPlan || addToPlanMutation.isPending}
                >
                  {addToPlanMutation.isPending ? "Adding..." : "Add to Plan"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}