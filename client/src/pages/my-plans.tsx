import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest, queryClient } from "@/lib/queryClient";

import CreatePlanModal from "@/components/create-plan-modal";
import AddVideoModal from "@/components/add-video-modal";
import SharePlanModal from "@/components/share-plan-modal";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";
import {
  BookOpen,
  Plus,
  Play,
  Share2,
  Clock,
  CheckCircle,
  Trash2,
  PlayCircle,
} from "lucide-react";

interface LearningPlan {
  id: number;
  title: string;
  description: string;
  slug: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PlanVideo {
  id: number;
  planId: number;
  videoId: number;
  orderIndex: number;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
}

export default function MyPlans() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { toast } = useToast();

  const { data: plans = [], isLoading: plansLoading } = useQuery({
    queryKey: ["/api/learning-plans"],
    enabled: isAuthenticated,
  });

  const deletePlanMutation = useMutation({
    mutationFn: async (planId: number) => {
      await apiRequest("DELETE", `/api/learning-plans/${planId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      toast({
        title: "Success",
        description: "Learning plan deleted successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete learning plan",
        variant: "destructive",
      });
    },
  });

  if (authLoading || plansLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your plans...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-slate-800 mb-2">
            Please Sign In
          </h2>
          <p className="text-slate-600 mb-4">
            You need to be signed in to view your learning plans.
          </p>
          <Button onClick={() => (window.location.href = "/login")}>
            Sign In
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">
            My Learning Plans
          </h1>
          <p className="text-slate-600 mt-2">
            Manage your personalized learning journey
          </p>
        </div>
        <CreatePlanModal>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white">
            <Plus className="w-4 h-4 mr-2" />
            Create New Plan
          </Button>
        </CreatePlanModal>
      </div>

      {/* Plans Grid */}
      {plans.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 text-slate-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-slate-800 mb-2">
            No Learning Plans Yet
          </h3>
          <p className="text-slate-600 mb-6">
            Create your first learning plan to get started
          </p>
          <CreatePlanModal>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Plan
            </Button>
          </CreatePlanModal>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {plans.map((plan: LearningPlan) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              onDelete={() => deletePlanMutation.mutate(plan.id)}
              isDeleting={deletePlanMutation.isPending}
            />
          ))}
        </div>
      )}

      {/* Discover New Content */}
      <Card className="bg-gradient-to-r from-blue-50 to-violet-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-xl text-blue-800">
            Discover New Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-blue-600 mb-4">
            Explore our library of educational videos and add them to your
            learning plans
          </p>
          <Link href="/explore">
            <Button
              variant="outline"
              className="border-blue-300 text-blue-600 hover:bg-blue-100"
            >
              Browse Video Library
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}

interface PlanCardProps {
  plan: LearningPlan;
  onDelete: () => void;
  isDeleting: boolean;
}

function PlanCard({ plan, onDelete, isDeleting }: PlanCardProps) {
  const { data: planVideos = [] } = useQuery({
    queryKey: [`/api/learning-plans/${plan.id}/videos`],
  });

  const { data: progress = [] } = useQuery({
    queryKey: [`/api/progress/plan/${plan.id}`],
  });

  const totalVideos = planVideos.length;
  const completedVideos = progress.filter(
    (p: VideoProgress) => p.isCompleted
  ).length;
  const progressPercentage =
    totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0;

  return (
    <Card className="group hover:shadow-lg transition-all duration-200">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div className="flex-grow">
            <CardTitle className="text-lg mb-2 group-hover:text-blue-600 transition-colors">
              <Link href={`/plan/${plan.slug}`} className="block">
                {plan.title}
              </Link>
            </CardTitle>
            <p className="text-sm text-slate-600 line-clamp-2 mb-3">
              {plan.description}
            </p>
            <div className="flex items-center space-x-4 text-sm text-slate-500">
              <span className="flex items-center">
                <PlayCircle className="w-4 h-4 mr-1" />
                {totalVideos} courses
              </span>
              <span className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1" />
                {completedVideos} completed
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-2">
            <span className="text-slate-600">Progress</span>
            <span className="font-medium text-slate-800">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Link href={`/plan/${plan.slug}`} className="flex-1">
            <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
              <Play className="w-4 h-4 mr-2" />
              {totalVideos > 0 ? "Continue" : "Add Courses"}
            </Button>
          </Link>

          <AddVideoModal planId={plan.id}>
            <Button variant="outline" size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </AddVideoModal>

          <SharePlanModal planId={plan.id} planTitle={plan.title}>
            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4" />
            </Button>
          </SharePlanModal>

          <Button
            variant="outline"
            size="sm"
            onClick={onDelete}
            disabled={isDeleting}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            {isDeleting ? (
              <div className="w-4 h-4 border-2 border-red-300 border-t-red-600 rounded-full animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
